FROM node:20.18-alpine3.19 as base

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
ENV PUPPETEER_EXECUTABLE_PATH /usr/bin/chromium-browser

WORKDIR /usr/src/app

RUN apk update && apk add --no-cache --virtual \
    .build-deps \
    udev \
    ttf-opensans \
    chromium \
    ca-certificates

FROM base as build

COPY ./package*.json .
RUN npm ci
COPY . .
RUN npm run build
RUN npm pkg delete scripts.prepare
RUN npm ci --only=production 

FROM base

COPY --from=build /usr/src/app .
EXPOSE 3003

WORKDIR /usr/src/app


CMD ["npm", "run", "start:prod", "--no-sandbox"]