{"name": "user-validator", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_ENV=local nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint-fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "NODE_ENV=testing jest --forceExit --testTimeout=120000 --detectOpenHandles --silent=false", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=testing jest --config ./test/jest-e2e.json --forceExit --testTimeout=120000 --detectOpenHandles --silent=false"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.758.0", "@nestjs/axios": "4.0.0", "@nestjs/common": "11.0.11", "@nestjs/core": "^11.0.11", "@nestjs/mongoose": "11.0.1", "@nestjs/platform-express": "11.0.11", "@nestjs/typeorm": "^11.0.0", "@sentry/node": "^9.3.0", "aws-sdk": "2.1692.0", "class-transformer": "0.5.1", "class-validator": "0.14.1", "dd-trace": "5.40.0", "dotenv": "16.4.7", "mongoose": "^8.12.0", "pg": "^8.13.3", "reflect-metadata": "0.2.2", "rxjs": "7.8.2", "typeorm": "^0.3.21", "whatsapp-web.js": "1.26.1-alpha.2"}, "devDependencies": {"@faker-js/faker": "^9.5.1", "@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.11", "@types/express": "^5.0.0", "@types/jest": "29.5.14", "@types/node": "22.13.9", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "jest": "29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/../src/$1", "^dd-trace$": "<rootDir>/../node_modules/dd-trace"}}}