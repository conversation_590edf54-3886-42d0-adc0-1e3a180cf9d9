import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import * as Sentry from '@sentry/node';
import { AppModule } from './app.module';
import { PropagaLogger } from './infrastructure/logging/logging.service';
import { json } from 'body-parser';
import { ErrorHandlerService } from './infrastructure/http/error-handler.service';
import { HttpService } from '@nestjs/axios';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new PropagaLogger(),
  });
  const httpService = new HttpService();
  const errorHandlerService = await app.get(ErrorHandlerService);

  Sentry.init({
    dsn: 'https://<EMAIL>/11',
    environment: process.env.NODE_ENV ?? 'local',
  });

  app.setGlobalPrefix('v1');
  app.use(json({ limit: '2mb' }));

  app.enableCors({
    allowedHeaders: '*',
    origin: '*',
    credentials: true,
  });

  Sentry.setupExpressErrorHandler(app);

  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));
  errorHandlerService.handleError(httpService);

  await app.listen(3003);
}
bootstrap();
