import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { WhatsappModule } from './infrastructure/whatsapp/whatsapp.module';
import { FileManagerModule } from './infrastructure/file-manager/file-manager.module';
import { PhoneNumberApiModule } from './infrastructure/external-services/phone-number-api/phone-number-api.module';
import { ConfigModule } from './infrastructure/config/config.module';
import { TokenValidationMiddleware } from './middleware/token';
import { PropagaDatabaseModule } from './infrastructure/propaga-database/propaga-database.module';
import { ScoreModule } from './application/score/score.module';
import { ZenpliModule } from './infrastructure/external-services/zenpli/zenpli.module';
import { MongoDatabaseModule } from './infrastructure/mongo-database/mongo-database.module';
import { UserLocationModule } from './application/user-location/user-location.module';
import { KycModule } from './application/kyc/kyc.module';
import { HttpModule } from './infrastructure/http/http.module';
import { BlacklistModule } from './application/blacklist/blacklist.module';
@Module({
  imports: [
    WhatsappModule,
    FileManagerModule,
    PhoneNumberApiModule,
    ConfigModule,
    PropagaDatabaseModule,
    ScoreModule,
    ZenpliModule,
    MongoDatabaseModule,
    UserLocationModule,
    KycModule,
    HttpModule,
    BlacklistModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenValidationMiddleware).forRoutes('*');
  }
}
