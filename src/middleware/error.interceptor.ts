import {
  ExecutionContext,
  Injectable,
  NestInterceptor,
  CallHandler,
  HttpStatus,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import * as Sentry from '@sentry/core';
import { AxiosError } from 'axios';

@Injectable()
export class SentryInterceptor implements NestInterceptor {
  toJSON(body: any) {
    try {
      return JSON.stringify(body);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return JSON.stringify({
        data: body,
      });
    }
  }
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      tap({
        error: (error) => {
          if (error.status === HttpStatus.NOT_FOUND) {
            return;
          }
          const request = context.switchToHttp().getRequest();
          const response = context.switchToHttp().getResponse();
          const body = this.toJSON(request.body);

          Sentry.withScope((scope) => {
            scope
              .setTransactionName(`Route: ${request.method} ${request.url}`)
              .setTag('trace-origin', 'middleware')
              .setContext('response', response)
              .setContext('request', {
                ...request,
                body,
              });

            if (error.config && error.response) {
              const axiosError = error as AxiosError;

              scope
                .setContext('axios request', {
                  data: axiosError.config.data,
                  headers: JSON.stringify(axiosError.config.headers),
                  method: axiosError.config.method,
                  url: axiosError.config.url,
                })
                .setContext('axios response', {
                  data: JSON.stringify(axiosError.response.data).replace('token', '***'),
                  headers: JSON.stringify(axiosError.response.headers),
                  statusCode: axiosError.response.status,
                });
            }

            Sentry.captureException(error);
            scope.clear();
          });
        },
      }),
    );
  }
}
