import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../infrastructure/constants';

export class TokenNotValidException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.TOKEN_NOT_VALID.code,
        errorMessage: ErrorCodes.TOKEN_NOT_VALID.message,
      },
      HttpStatus.UNAUTHORIZED,
    );
  }
}

export class TokenNotFoundException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.TOKEN_NOT_FOUND.code,
        errorMessage: ErrorCodes.TOKEN_NOT_FOUND.message,
      },
      HttpStatus.UNAUTHORIZED,
    );
  }
}
