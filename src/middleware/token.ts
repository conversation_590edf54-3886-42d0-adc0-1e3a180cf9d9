import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { TokenNotValidException, TokenNotFoundException } from './exceptions';
import { ConfigService } from 'src/infrastructure/config/config.service';

@Injectable()
export class TokenValidationMiddleware implements NestMiddleware {
  constructor(private readonly configService: ConfigService) {}

  async use(req: Request, _: Response, next: NextFunction) {
    const token = req.header('Authorization');

    if (!token) {
      throw new TokenNotFoundException();
    }

    const secretToken = await this.configService.get('token_access');

    const isSameSecretToken = secretToken === token;

    if (!isSameSecretToken) {
      throw new TokenNotValidException();
    }

    next();
  }
}
