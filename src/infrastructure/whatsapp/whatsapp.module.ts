import { Lo<PERSON>, <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { WhatsappService } from './whatsapp.service';

import WAWebJS, { Client, LocalAuth, RemoteAuth } from 'whatsapp-web.js';
import { WhatsappClient } from './client';
import { FileManagerService } from '../file-manager/file-manager.service';
import { FileManagerModule } from '../file-manager/file-manager.module';
import { StoreWhatsapp } from './store';

const DEFAULT_ENV = 'development';

@Module({
  providers: [WhatsappService, WhatsappClient],
  exports: [WhatsappService],
  imports: [FileManagerModule],
})
export class WhatsappModule implements OnModuleInit {
  private logger = new Logger(WhatsappModule.name);

  constructor(
    private readonly whatsappClient: WhatsappClient,
    private readonly fileManagerService: FileManagerService,
  ) {}

  async getSessionFile() {
    const env = process.env.NODE_ENV ?? DEFAULT_ENV;
    const file = await this.fileManagerService.getFile({
      bucketName: 'propaga-validation-artifacts',
      fileName: `sessión-web-whatsapp-${env}.zip`,
    });

    return file;
  }

  private async getAuthStrategy() {
    if (process.env.NODE_ENV === 'local') {
      return new LocalAuth();
    }

    const sessionZip = await this.getSessionFile();
    return new RemoteAuth({
      store: new StoreWhatsapp({
        fileManagerService: this.fileManagerService,
        logger: this.logger,
        sessionZip,
      }),
      backupSyncIntervalMs: 60_000,
    });
  }

  async onModuleInit() {
    this.logger.log('Starting whatsapp module');

    this.whatsappClient.client = new Client({
      authStrategy: await this.getAuthStrategy(),
      restartOnAuthFail: true,
      puppeteer: {
        headless: true,
        args: ['--no-sandbox'],
      },
    });
    this.whatsappClient.client.on('qr', (qr: string) => this.logger.log(qr));
    this.whatsappClient.client.on('ready', () => this.logger.log('Client is ready!'));
    this.whatsappClient.client.on('disconnected', (reason: WAWebJS.WAState | 'NAVIGATION') =>
      this.logger.error('Client is disconnected with reason: ' + reason.toString()),
    );

    this.whatsappClient.client.on('message', async (message) => {
      if (message.body === process.env.NODE_ENV) {
        await message.reply(`I'm alive in ${process.env.NODE_ENV}`);
      }
    });

    this.whatsappClient.client.initialize();
  }
}
