import { Logger } from '@nestjs/common';
import * as fs from 'node:fs';

const DEFAULT_ENV = 'development';
export class StoreWhatsapp {
  logger: Logger;
  sessionZip: any;
  fileManagerService: any;

  constructor({ logger, sessionZip, fileManagerService }) {
    this.logger = logger;
    this.sessionZip = sessionZip;
    this.fileManagerService = fileManagerService;
  }
  async sessionExists(): Promise<boolean> {
    return !!this.sessionZip;
  }

  async delete(options) {
    this.logger.log('Trying to remove the session');
    this.logger.log(options);
  }

  async save(options) {
    const env = process.env.NODE_ENV ?? DEFAULT_ENV;
    const { writeStream, promise } = this.fileManagerService.uploadStream({
      bucketName: 'propaga-validation-artifacts',
      fileName: `sessión-web-whatsapp-${env}.zip`,
      contentType: 'application/zip',
    });
    const readStream = fs.createReadStream(`${options.session}.zip`);
    readStream.pipe(writeStream);

    await promise;

    this.logger.log('Saved WA session successfull');
  }

  async extract(options) {
    const bufferSession = this.sessionZip as Buffer;

    fs.writeFileSync(options.path, bufferSession, 'binary');

    this.logger.log('Extracted successfull WA session');
  }
}
