import { Injectable, Logger } from '@nestjs/common';
import { WhatsappClient } from './client';
import { ErrorCodes } from 'src/application/constants';

@Injectable()
export class WhatsappService {
  logger = new Logger(WhatsappService.name);

  constructor(private readonly whatsappClient: WhatsappClient) {}

  private async mockResponse(
    phoneNumber: string,
  ): Promise<{ isValid: boolean; context: any; error: any }> {
    const isValid = Math.random() > 0.5;

    return {
      isValid,
      error: !isValid
        ? {
            errorCode: ErrorCodes.WHATSAPP_NOT_EXISTS.code,
            errorMessage: ErrorCodes.WHATSAPP_NOT_EXISTS.message,
            errorValue: phoneNumber,
          }
        : null,
      context: {
        response: !isValid
          ? {
              errorCode: ErrorCodes.WHATSAPP_NOT_EXISTS.code,
              errorMessage: ErrorCodes.WHATSAPP_NOT_EXISTS.message,
              errorValue: phoneNumber,
            }
          : {},
        phoneNumber,
      },
    };
  }

  async verifyPhoneNumber(
    phoneNumber: string,
  ): Promise<{ isValid: boolean; context: any; error: any }> {
    try {
      const formattedPhoneNumber = `52${phoneNumber}`;

      if (process.env.NODE_ENV === 'local') {
        return this.mockResponse(phoneNumber);
      }

      const response = await this.whatsappClient.client.getNumberId(formattedPhoneNumber);

      return {
        isValid: !!response,
        error: response ?? {
          errorCode: ErrorCodes.WHATSAPP_NOT_EXISTS.code,
          errorMessage: ErrorCodes.WHATSAPP_NOT_EXISTS.message,
          errorValue: phoneNumber,
        },
        context: {
          response,
          phoneNumber,
        },
      };
    } catch (error) {
      this.logger.error(error);
      return {
        isValid: false,
        error: {
          errorCode: ErrorCodes.UNKNOWN_ERROR.code,
          errorMessage: error.message,
        },
        context: {
          response: error.errorCode ?? error,
          phoneNumber,
        },
      };
    }
  }
}
