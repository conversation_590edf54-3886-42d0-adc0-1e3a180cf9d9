import { Test, TestingModule } from '@nestjs/testing';
import { WhatsappService } from './whatsapp.service';
import { WhatsappClient } from './client';

describe('WhatsappService', () => {
  let service: WhatsappService;
  let whatsappClient: WhatsappClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: WhatsappClient,
          useValue: {
            client: {
              getNumberId: jest.fn(),
            },
          },
        },
        WhatsappService,
      ],
    }).compile();

    service = module.get<WhatsappService>(WhatsappService);
    whatsappClient = module.get<WhatsappClient>(WhatsappClient);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    jest.spyOn(whatsappClient.client, 'getNumberId').mockResolvedValue(null);

    const result = await service.verifyPhoneNumber('**********');

    expect(result).toEqual({
      isValid: false,
      error: {
        errorCode: 'WHATSAPP_NOT_EXISTS',
        errorMessage: 'This number doesnt have a whatsapp account',
        errorValue: '**********',
      },
      context: {
        response: null,
        phoneNumber: '**********',
      },
    });
  });
});
