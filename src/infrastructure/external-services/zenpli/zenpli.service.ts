import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from 'src/infrastructure/config/config.service';
import { EMPTY_STRING, ZENPLI_SCENARIO, MEXICO_PHONE_PREFIX } from 'src/infrastructure/constants';
import { ZenpliResponse } from './interfaces/response';

@Injectable()
export class ZenpliService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async login() {
    const zenpliUrl = await this.configService.get('zenpli_url');
    const zenpliClientId = await this.configService.get('zenpli_client_id');
    const zenpliClientSecret = await this.configService.get('zenpli_client_secret');

    const config = {
      method: 'post',
      url: `${zenpliUrl}/v1/login`,
      headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
      data: {
        client_id: zenpliClientId,
        client_secret: zenpliClientSecret,
      },
    };

    const { data } = await firstValueFrom(this.httpService.request(config));
    return data.access_token;
  }

  private validateInsights(phoneNumber: string, { insights, scores }: ZenpliResponse) {
    if (!insights.phone_active) {
      return {
        phoneNumber,
        isValid: false,
        reason: `Phone number ${phoneNumber} is not active`,
        errorValue: phoneNumber,
        insights,
        scores,
      };
    }

    return {
      phoneNumber,
      isValid: true,
      insights,
      scores,
    };
  }

  async validateCustomer(
    accessToken: string,
    phoneNumber: string,
  ): Promise<{
    isValid: boolean;
    insights: any;
    reason?: string;
    errorValue?: string;
    scores: any;
  }> {
    const zenpliUrl = await this.configService.get('zenpli_url');

    const config = {
      method: 'post',
      url:
        process.env.NODE_ENV !== 'production'
          ? `${zenpliUrl}/sandbox/identity-score`
          : `${zenpliUrl}/v1/identity-score`,
      data: {
        scenario_id: process.env.NODE_ENV !== 'production' ? EMPTY_STRING : ZENPLI_SCENARIO,
        user: {
          phone: { number: phoneNumber, prefix: MEXICO_PHONE_PREFIX },
        },
      },
    };

    const response = await firstValueFrom(
      this.httpService.request({
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        ...config,
      }),
    );
    return this.validateInsights(phoneNumber, response.data);
  }
}
