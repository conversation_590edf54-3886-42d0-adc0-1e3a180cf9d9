export interface ZenpliResponse {
  reference_id: string;
  scores: {
    zenpli_score: number;
  };
  insights: {
    email_validation: boolean;
    email_network_first_seen_days: number;
    email_to_full_name: string;
    email_social_registered_profiles: number;
    email_social_registered_consumer_electronics_profiles: number;
    email_social_registered_email_provider_profiles: number;
    email_social_registered_ecommerce_profiles: number;
    email_social_registered_social_media_profiles: number;
    email_social_registered_messaging_profiles: number;
    email_social_registered_professional_profiles: number;
    email_social_registered_entertainment_profiles: number;
    email_social_registered_travel_profiles: number;
    email_social_number_of_names_returned: number;
    email_social_number_of_photos_returned: number;
    email_deliverable_validation: boolean;
    email_domain_name: string;
    email_tld: string;
    email_disposable_validation: boolean;
    email_domain_free_provider_flag: boolean;
    email_spf_strict_validation: boolean;
    email_suspicious_tld_flag: boolean;
    email_is_breached_flag: boolean;
    email_number_of_breaches: number;
    email_first_breach: Date;
    email_last_breach: Date;
    email_tenure: number;
    phone_validation: boolean;
    phone_line_type: string;
    phone_carrier: string;
    phone_country_code: string;
    phone_network_last_seen_days: number;
    phone_to_email_first_seen_days: number;
    phone_to_full_name: string;
    phone_to_address: string;
    phone_disposable_validation: boolean;
    phone_active: boolean;
    phone_ported_validation: boolean;
    phone_ported_history_number_of_ports: number;
    phone_ported_since_x_days: number;
    phone_ported_current_carrier: string;
    phone_ported_original_carrier: string;
    phone_social_registered_profiles: number;
    phone_social_registered_email_provider_profiles: number;
    phone_social_registered_ecommerce_profiles: number;
    phone_social_registered_social_media_profiles: number;
    phone_social_registered_professional_profiles: number;
    phone_social_registered_messaging_profiles: number;
    phone_social_last_activity: number;
    phone_social_number_of_names_returned: number;
    phone_social_number_of_photos_returned: number;
    phone_tenure_min: number;
    phone_tenure_max: number;
    ip_to_phone_distance: number;
    address_validity_level: string;
    address_to_full_name: string;
    address_input_validity_level: string;
    address_geocode_validity_level: string;
    address_complete_validation: boolean;
    address_unconfirmed_components_flag: boolean;
    address_has_inferred_components: boolean;
    address_formatted: string;
    address_type: string;
    ip_risk: boolean;
    ip_risk_score: number;
    ip_last_seen_days: number;
    ip_country_code: string;
    ip_subdivision: string;
    ip_to_address_distance: number;
    ip_city: string;
    ip_latitude: number;
    ip_longitude: number;
    ip_internet_service_provider: string;
    ip_connection_type: string;
    ip_organization: string;
    ip_crawler_flag: boolean;
    ip_host: string;
    ip_proxy_flag: boolean;
    ip_vpn_flag: boolean;
    ip_tor_flag: boolean;
    ip_active_vpn_flag: boolean;
    ip_active_tor_flag: boolean;
    ip_network_recent_abuse: boolean;
    ip_bot_status: boolean;
    ip_network_abuse_velocity: string;
    identity_network_score: number;
    identity_risk_score: number;
  };
}
