import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from 'src/infrastructure/config/config.service';
import { COUNTRY_CODE, COUNTRY_CODE_NUMBER, ZERO } from 'src/application/constants';
import { UnknownErrorException } from 'src/application/score/exceptions/unknown-error';
import { InvalidPhoneNumberException } from 'src/application/score/exceptions/invalid-phone-number';

const FAKE_NUMBER_NOT_FOUND = '2223412312';
const FAKE_NUMBER_MANY_REQUEST = '9872323112';

@Injectable()
export class PhoneNumberApiService {
  logger = new Logger(PhoneNumberApiService.name);
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  private formatPhoneNumber(phoneNumber: string) {
    if (phoneNumber.startsWith(COUNTRY_CODE, ZERO)) {
      return phoneNumber.substring(3, phoneNumber.length);
    }

    if (phoneNumber.startsWith(COUNTRY_CODE_NUMBER, ZERO)) {
      return phoneNumber.substring(2, phoneNumber.length);
    }

    return phoneNumber;
  }

  async validatePhoneNumber(phoneNumber: string) {
    const phoneValidatorUrl = await this.configService.get('phone_validator_url');
    const phoneValidatorToken = await this.configService.get('phone_validator_token');
    const phoneValidatorKey = await this.configService.get('phone_validator_key');

    const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);

    const config = {
      method: 'GET',
      url: `${phoneValidatorUrl}/${formattedPhoneNumber}`,
      headers: {
        Authorization: phoneValidatorToken,
        'X-RapidAPI-Key': phoneValidatorKey,
      },
    };

    if (process.env.NODE_ENV !== 'production') {
      config['headers']['Prefer'] = 'code=200';

      if (phoneNumber === FAKE_NUMBER_NOT_FOUND) {
        config['headers']['Prefer'] = 'code=404';
      }

      if (phoneNumber === FAKE_NUMBER_MANY_REQUEST) {
        config['headers']['Prefer'] = 'code=429';
      }
    }

    try {
      await firstValueFrom(this.httpService.request(config));
    } catch (error) {
      if (error.response.status === HttpStatus.NOT_FOUND) {
        this.logger.error(`Validation failed for phone number ${phoneNumber}`);
        throw new InvalidPhoneNumberException(phoneNumber);
      }

      this.logger.error(`Error on request to phone-validation-api`, error.response.data);
      throw new UnknownErrorException(error.response.data);
    }

    this.logger.log(`Number validated ${phoneNumber}`);
    return true;
  }
}
