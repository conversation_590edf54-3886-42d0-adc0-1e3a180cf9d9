import { Injectable } from '@nestjs/common';
import { ManagedUpload } from 'aws-sdk/clients/s3';
import * as AWS from 'aws-sdk';
import * as stream from 'node:stream';
import { Readable } from 'node:stream';
const REGION = 'us-west-2';

interface UploadFileToS3Props {
  buffer: Buffer | Uint8Array | Blob | string | Readable;
  bucketName: string;
  fileName: string;
  contentType: string;
}

interface GetFileProps {
  bucketName: string;
  fileName: string;
}

@Injectable()
export class FileManagerService {
  public async uploadFile({
    buffer,
    bucketName,
    fileName,
    contentType,
  }: UploadFileToS3Props): Promise<ManagedUpload.SendData> {
    const s3 = new AWS.S3({
      region: REGION,
    });
    const params: AWS.S3.PutObjectRequest = {
      Bucket: bucketName,
      Key: fileName,
      Body: buffer,
      ContentType: contentType,
    };

    return s3.upload(params).promise();
  }

  public uploadStream({ bucketName, fileName, contentType }) {
    const pass = new stream.PassThrough();
    return {
      writeStream: pass,
      promise: this.uploadFile({
        bucketName,
        fileName,
        contentType,
        buffer: pass,
      }),
    };
  }

  public async getFile({ fileName, bucketName }: GetFileProps) {
    const s3 = new AWS.S3({
      region: REGION,
    });

    const params: AWS.S3.GetObjectRequest = {
      Bucket: bucketName,
      Key: fileName,
    };

    try {
      const file = await s3.getObject(params).promise();
      return file.Body;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      return null;
    }
  }
}
