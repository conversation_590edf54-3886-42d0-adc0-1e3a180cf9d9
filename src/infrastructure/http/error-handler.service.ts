import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ErrorHandlerService {
  logger = new Logger(ErrorHandlerService.name);

  private safeJsonStringify(data: unknown): string {
    if (typeof data === 'string') {
      return data;
    }

    return JSON.stringify(data);
  }

  public handleError(httpService: HttpService): void {
    httpService.axiosRef.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        this.logger.error('HTTP request failed', {
          config: {
            url: error.config?.url,
            method: error.config?.method,
            data: this.safeJsonStringify(error.config?.data),
          },
          response: {
            data: this.safeJsonStringify(error.response?.data),
          },
        });

        return Promise.reject(error);
      },
    );
  }
}
