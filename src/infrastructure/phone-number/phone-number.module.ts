import { Module } from '@nestjs/common';
import { PhoneNumberService } from './phone-number.service';
import { ZenpliModule } from '../external-services/zenpli/zenpli.module';
import { MongoDatabaseModule } from '../mongo-database/mongo-database.module';

@Module({
  providers: [PhoneNumberService],
  exports: [PhoneNumberService],
  imports: [ZenpliModule, MongoDatabaseModule],
})
export class PhoneNumberModule {}
