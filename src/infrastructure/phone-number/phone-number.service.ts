import { Injectable, Logger } from '@nestjs/common';
import { ZenpliService } from '../external-services/zenpli/zenpli.service';
import { MEXICO_PHONE_PREFIX } from '../constants';
import { ValidatorRepository } from '../mongo-database/repositories/validator.service';

@Injectable()
export class PhoneNumberService {
  logger = new Logger(PhoneNumberService.name);
  constructor(
    private readonly zenpliService: ZenpliService,
    private readonly validatorRepository: ValidatorRepository,
  ) {}

  private formatPhoneNumber(phoneNumber: string): string {
    if (phoneNumber.startsWith(MEXICO_PHONE_PREFIX, 0)) {
      return phoneNumber.substring(2, phoneNumber.length);
    }

    if (phoneNumber.startsWith(`+${MEXICO_PHONE_PREFIX}`, 0)) {
      return phoneNumber.substring(3, phoneNumber.length);
    }

    return phoneNumber;
  }

  async validate(phoneNumbers: string[]): Promise<
    {
      phoneNumber: string;
      isValid: boolean;
      insights: any;
      reason?: string;
      errorValue?: string;
      scores: any;
    }[]
  > {
    const accessToken = await this.zenpliService.login();
    const results = [];

    for (const phoneNumber of phoneNumbers) {
      const phoneNumberMapped = this.formatPhoneNumber(phoneNumber);

      const result = await this.zenpliService.validateCustomer(accessToken, phoneNumberMapped);

      await this.validatorRepository.create({
        phoneNumber: phoneNumberMapped,
        isValid: result.isValid,
        insights: result.insights,
        scores: result.scores,
      });

      results.push(result);

      this.logger.log(`Number validated ${phoneNumber}`);
    }

    return results;
  }
}
