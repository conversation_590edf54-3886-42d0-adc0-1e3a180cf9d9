import { Module } from '@nestjs/common';
import { MongooseModule, MongooseModuleFactoryOptions } from '@nestjs/mongoose';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { Validator, ValidatorSchema } from './models/validator.entity';
import { ValidatorRepository } from './repositories/validator.service';
import { LocationSchema, Location } from './models/location.entity';
import { LocationRepository } from './repositories/location.service';
import { KycRepository } from './repositories/kyc.service';
import { Kyc, KycSchema } from './models/kyc.entity';
import { KycReason, KycReasonSchema } from './models/kyc-reason.entiy';
import { KycReasonsRepository } from './repositories/kyc-reason.service';
import {
  PhoneNumbersBlacklistSchema,
  PhoneNumbersBlacklist,
} from './models/phone-numbers-blacklist';
import { PhoneNumbersBlacklistRepository } from './repositories/phone-numbers-blacklist.service';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => {
        const mongoUri = await config.get('mongo_host');

        return {
          uri: mongoUri,
        } as MongooseModuleFactoryOptions;
      },
    }),
    MongooseModule.forFeature([{ name: Validator.name, schema: ValidatorSchema }]),
    MongooseModule.forFeature([{ name: Location.name, schema: LocationSchema }]),
    MongooseModule.forFeature([{ name: Kyc.name, schema: KycSchema }]),
    MongooseModule.forFeature([{ name: KycReason.name, schema: KycReasonSchema }]),
    MongooseModule.forFeature([
      { name: PhoneNumbersBlacklist.name, schema: PhoneNumbersBlacklistSchema },
    ]),
  ],
  providers: [
    ValidatorRepository,
    LocationRepository,
    KycRepository,
    KycReasonsRepository,
    PhoneNumbersBlacklistRepository,
  ],
  exports: [
    ValidatorRepository,
    LocationRepository,
    KycRepository,
    KycReasonsRepository,
    PhoneNumbersBlacklistRepository,
  ],
})
export class MongoDatabaseModule {}
