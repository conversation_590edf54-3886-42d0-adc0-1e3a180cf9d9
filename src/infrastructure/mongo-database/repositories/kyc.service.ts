import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Kyc } from '../models/kyc.entity';
import { Model } from 'mongoose';

@Injectable()
export class KycRepository {
  constructor(@InjectModel(Kyc.name) private kycModel: Model<Kyc>) {}

  create(kyc: { verificationId: string; dataRaw: object; userId: string; requestId: string }) {
    return new this.kycModel(kyc).save();
  }

  findRequestByVerificationId(verificationId: string) {
    return this.kycModel.findOne(
      { verificationId },
      { dataRaw: 1, verificationId: 1, requestId: 1, userId: 1, createdAt: 1 },
      { sort: { createdAt: -1 } },
    ) as Promise<Kyc>;
  }
}
