import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PhoneNumbersBlacklist } from '../models/phone-numbers-blacklist';

@Injectable()
export class PhoneNumbersBlacklistRepository {
  constructor(
    @InjectModel(PhoneNumbersBlacklist.name)
    private phoneNumbersBlacklistModel: Model<PhoneNumbersBlacklist>,
  ) {}

  async create(phoneNumberBlacklisted: {
    phoneNumber: string;
    reason: string;
  }): Promise<PhoneNumbersBlacklist> {
    return await new this.phoneNumbersBlacklistModel(phoneNumberBlacklisted).save();
  }

  async findAll() {
    return await this.phoneNumbersBlacklistModel.find();
  }

  async findByPhoneNumber(phoneNumber: string) {
    return await this.phoneNumbersBlacklistModel.findOne({ phoneNumber });
  }
}
