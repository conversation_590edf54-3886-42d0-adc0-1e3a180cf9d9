import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Location } from '../models/location.entity';

@Injectable()
export class LocationRepository {
  constructor(@InjectModel(Location.name) private locationModel: Model<Location>) {}

  create(location: {
    latitude: string;
    longitude: string;
    phoneNumber: string;
    userId: string;
    name: string;
  }) {
    return new this.locationModel(location).save();
  }

  findAll() {
    return this.locationModel.find();
  }
}
