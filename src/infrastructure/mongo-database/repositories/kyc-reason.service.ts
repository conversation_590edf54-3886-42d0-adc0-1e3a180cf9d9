import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { KycReason } from '../models/kyc-reason.entiy';

@Injectable()
export class KycReasonsRepository {
  constructor(@InjectModel(KycReason.name) private kycReasonModel: Model<KycReason>) {}

  create(kycReason: { reason: string; translatedReason: string }) {
    return new this.kycReasonModel(kycReason).save();
  }

  findKycReasonByReason(reason: string) {
    return this.kycReasonModel.findOne(
      { reason },
      { reason: 1, translatedReason: 1 },
      { sort: { createdAt: -1 } },
    ) as Promise<KycReason>;
  }
}
