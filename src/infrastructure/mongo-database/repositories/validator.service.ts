import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Validator } from '../models/validator.entity';
import { Model } from 'mongoose';

@Injectable()
export class ValidatorRepository {
  constructor(@InjectModel(Validator.name) private validatorModel: Model<Validator>) {}

  async create(validator: {
    phoneNumber: string;
    isValid: boolean;
    insights: object;
    scores: object;
  }) {
    return await new this.validatorModel(validator).save();
  }
}
