import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type LocationDocument = HydratedDocument<Location>;

@Schema()
export class Location {
  @Prop({ required: true })
  latitude: string;

  @Prop({ required: true })
  longitude: string;

  @Prop({ required: true })
  phoneNumber: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  name: string;
}

export const LocationSchema = SchemaFactory.createForClass(Location);

LocationSchema.set('timestamps', true);
