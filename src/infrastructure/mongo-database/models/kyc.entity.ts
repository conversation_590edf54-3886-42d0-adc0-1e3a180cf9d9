import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type LocationDocument = HydratedDocument<Location>;

@Schema()
export class Kyc {
  @Prop({ id: true, required: true, index: true })
  verificationId: string;

  @Prop({ required: true, type: Object })
  dataRaw: Record<string, any>;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  requestId: string;
}

export const KycSchema = SchemaFactory.createForClass(Kyc);

KycSchema.set('timestamps', true);
