import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type KycReasonDocument = HydratedDocument<KycReason>;

@Schema()
export class KycReason {
  @Prop({ required: true })
  reason: string;

  @Prop({ required: true })
  translatedReason: string;
}

export const KycReasonSchema = SchemaFactory.createForClass(KycReason);

KycReasonSchema.set('timestamps', true);
