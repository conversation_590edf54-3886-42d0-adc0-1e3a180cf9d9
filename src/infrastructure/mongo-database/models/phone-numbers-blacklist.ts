import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type PhoneNumberBlacklistDocument = HydratedDocument<PhoneNumbersBlacklist>;

@Schema()
export class PhoneNumbersBlacklist {
  @Prop({ required: true })
  phoneNumber: string;

  @Prop({ required: true })
  reason: string;
}

export const PhoneNumbersBlacklistSchema = SchemaFactory.createForClass(PhoneNumbersBlacklist);

PhoneNumbersBlacklistSchema.set('timestamps', true);
