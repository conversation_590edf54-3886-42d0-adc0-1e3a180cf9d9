import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, SchemaTypes } from 'mongoose';

export type ValidatorDocument = HydratedDocument<Validator>;

@Schema()
export class Validator {
  @Prop({ required: true })
  phoneNumber: string;

  @Prop({ required: true })
  isValid: boolean;

  @Prop({ required: true, type: SchemaTypes.Map })
  insights: object;

  @Prop({ required: true, type: SchemaTypes.Map })
  scores: object;
}

export const ValidatorSchema = SchemaFactory.createForClass(Validator);

ValidatorSchema.set('timestamps', true);
