import { Injectable, Logger } from '@nestjs/common';
import { BedrockRuntimeClient, ConverseCommand } from '@aws-sdk/client-bedrock-runtime';
import { Message } from '@aws-sdk/client-bedrock-runtime/dist-types/models/models_0';

interface TextTranslatorResponse {
  originalText: string;
  score: number;
  translatedText: string;
}

@Injectable()
export class TextTranslatorService {
  private logger = new Logger(TextTranslatorService.name);

  public async handler(text: string): Promise<TextTranslatorResponse> {
    const client = new BedrockRuntimeClient({ region: 'us-west-2' });

    const conversation = [
      {
        role: 'user',
        content: [
          {
            text,
          },
        ],
      },
    ] as Message[];

    try {
      const converseResponse = await client.send(
        new ConverseCommand({
          modelId: 'anthropic.claude-3-haiku-20240307-v1:0',
          messages: conversation,
          system: [
            {
              text: 'You are a system to translate a text from english to spanish',
            },
            {
              text: 'The text you receive will be in english corresponing to an error reason from a external service',
            },
            {
              text: 'You have to be clear and concise',
            },
            {
              text: 'Respond in spanish',
            },
            {
              text: 'Follow this schema for the response: { "originalText": string, "score": 0, "translatedText": "string" } where score is a number between 0 and 100 indicating the confidence level of the validity of your translation, "originalText" is the original text, and "translatedText" is the translated text. Only respond with this schema',
            },
          ],
        }),
      );

      const response: TextTranslatorResponse = JSON.parse(
        converseResponse.output.message.content[0].text,
      );

      return response;
    } catch (error) {
      this.logger.error(error);
    }
  }
}
