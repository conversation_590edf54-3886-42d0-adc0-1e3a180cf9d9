import { Injectable, Logger } from '@nestjs/common';
import { BedrockRuntimeClient, ConverseCommand } from '@aws-sdk/client-bedrock-runtime';
import { Message } from '@aws-sdk/client-bedrock-runtime/dist-types/models/models_0';
import { Scores } from 'src/application/score/constants';

type ValidateUserNameResponse = {
  isValid: boolean;
  score: number;
  context: string;
};
@Injectable()
export class NameValidationService {
  logger = new Logger(NameValidationService.name);

  async handler(name: string): Promise<{ isValid: boolean; score: number; context: string }> {
    const client = new BedrockRuntimeClient({ region: 'us-west-2' });

    const conversation = [
      {
        role: 'user',
        content: [
          {
            text: name,
          },
        ],
      },
    ] as Message[];

    try {
      const converseResponse = await client.send(
        new ConverseCommand({
          modelId: 'anthropic.claude-3-haiku-20240307-v1:0',
          messages: conversation,
          system: [
            {
              text: 'You are a system to verify if the name of a person is valid, you have a deep understanding of the differences between a real name or a fake one',
            },
            {
              text: 'Evaluate every word for its validity, not just the name as a whole. For example, "<PERSON> <PERSON>e" would be evaluated as two separate words. If any of the words are invalid, the name is invalid.',
            },
            {
              text: 'The name would not contain numbers, question marks, or special characters',
            },
            {
              text: 'The name would not contain emojis',
            },
            {
              text: 'The name would have a first name and last name',
            },
            {
              text: 'The name would not be a business name',
            },
            {
              text: 'The meaning of the name would be a real given name and not a nickname',
            },
            {
              text: 'The name would be a name in spanish or latin american origin',
            },
            {
              text: 'Respond in spanish',
            },
            {
              text: 'Follow this schema for the response: { "isValid": true or false, "score": 0, "context": "string" } where score is a number between 0 and 100 indicating the confidence level of the validity of the name, "isValid" is a boolean indicating if the name is likely real, and "context" provides an explanation of how you determined the validity of the name.',
            },
          ],
        }),
      );

      const response: ValidateUserNameResponse = JSON.parse(
        converseResponse.output.message.content[0].text,
      );

      return response;
    } catch (error) {
      this.logger.error(error);
      return {
        isValid: false,
        score: Scores.REVIEW_VALUE,
        context: 'An error occurred while validating the name',
      };
    }
  }
}
