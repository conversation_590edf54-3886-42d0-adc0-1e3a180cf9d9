import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import { Users } from '../models/users.entity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(Users)
    private readonly user: Repository<Users>,
  ) {}

  async findUserByPhoneNumber(userId: string, phoneNumber: string): Promise<Users> {
    return this.user.findOne({
      where: {
        id: Not(userId),
        phoneNumber,
      },
    });
  }

  findUsersStatusByPhoneNumber(phoneNumbers: string[]): Promise<Users[]> {
    return this.user.find({
      where: {
        phoneNumber: In(phoneNumbers),
      },
      relations: ['status'],
    });
  }

  findUserStatusByPhoneNumber(phoneNumber: string): Promise<Users> {
    return this.user.findOne({
      where: {
        phoneNumber,
      },
      relations: ['status'],
    });
  }

  findUserStatusById(id: string): Promise<Users> {
    return this.user.findOne({
      where: {
        id,
      },
      relations: ['status'],
    });
  }
}
