import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Repository } from 'typeorm';
import { CornerStore } from '../models/corner-store.entity';
import { EMPTY_SPACE } from 'src/application/constants';

@Injectable()
export class CornerStoreRepository {
  constructor(
    @InjectRepository(CornerStore)
    private readonly cornerStore: Repository<CornerStore>,
  ) {}

  async findAllLocations(): Promise<object[]> {
    const cornerStores = await this.cornerStore.find({
      select: ['location', 'users'],
      where: {
        location: Not(IsNull()),
        users: {
          phoneNumber: Not(IsNull()),
        },
      },
      relations: ['users'],
    });

    return cornerStores.map((cornerStore) => {
      const [user] = cornerStore.users;
      return {
        userId: user.id,
        phoneNumber: user.phoneNumber,
        name: `${user.firstName} ${user.lastNames || EMPTY_SPACE}`,
        latitude: Number(cornerStore.location['latitude']),
        longitude: Number(cornerStore.location['longitude']),
      };
    });
  }
}
