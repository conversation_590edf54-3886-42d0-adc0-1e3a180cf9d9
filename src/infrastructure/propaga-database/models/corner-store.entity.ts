import { <PERSON>tity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { Users } from './users.entity';

@Entity({ name: 'CornerStore', schema: 'Propaga' })
export class CornerStore {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  cornerStoreName: string;

  @Column({
    type: 'decimal',
  })
  creditLimit: number;

  @Column({
    type: 'decimal',
  })
  originalCreditLimit: number;

  @Column({ type: 'decimal' })
  creditLimitNCycle: number;

  @Column({ default: false })
  verified: boolean;

  @Column()
  address: string;

  @Column({ type: 'json' })
  location: object;

  @Column({
    type: 'timestamp',
  })
  public createdAt: Date;

  @Column({
    type: 'timestamp',
  })
  public updatedAt: Date;

  @OneToMany(() => Users, (users) => users.cornerStore)
  users: Users[];
}
