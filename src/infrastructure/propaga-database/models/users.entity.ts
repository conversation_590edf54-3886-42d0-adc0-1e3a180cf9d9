import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne } from 'typeorm';
import { CornerStore } from './corner-store.entity';
import { UserStatus } from './user-status.entity';

@Entity({ name: 'Users', schema: 'Propaga' })
export class Users {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  phoneNumber: string;

  @Column()
  firstName: string;

  @Column()
  lastNames: string;

  @Column()
  email: string;

  @Column({ type: 'json' })
  dataVerification: object;

  @Column({ default: false })
  isBlockedToSendMessages: boolean;

  @Column({ type: 'timestamp' })
  public createdAt: Date;

  @Column({ type: 'timestamp' })
  public updatedAt: Date;

  @Column({ type: 'timestamp' })
  public registerDate: Date;

  @ManyToOne(() => CornerStore, (cornerStore) => cornerStore.users)
  cornerStore: CornerStore;

  @ManyToOne(() => UserStatus, (userStatus) => userStatus.users)
  status: UserStatus;
}
