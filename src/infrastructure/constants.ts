export const ErrorCodes = Object.freeze({
  TOKEN_NOT_VALID: {
    code: 'TOKEN_NOT_VALID',
    message: 'Your token is not valid',
  },
  TOKEN_NOT_FOUND: {
    code: 'TOKEN_NOT_FOUND',
    message: 'Your should send your token',
  },
});

export const MEXICO_PHONE_PREFIX = '52';
export const EMPTY_STRING = '';

export const ZENPLI_SCENARIO = 'a992a1ed-b2aa-4517-8117-a2e85536d6ce';

export const Environments = Object.freeze({
  TESTING: 'testing',
  LOCAL: 'local',
  PRODUCTION: 'production',
  STAGING: 'staging',
  DEVELOP: 'develop',
});

export const MOCKED_RESPONSE_TYPES = Object.freeze({
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  REVIEW: 'REVIEW',
  CONTACT_IS_AUTORREFERED: 'CONTACT_IS_AUTORREFERED',
  CONTACT_INVALID_WHATSAPP: 'CONTACT_INVALID_WHATSAPP',
});
