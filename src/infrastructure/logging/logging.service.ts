import { LoggerService, LogLevel } from '@nestjs/common';
import * as Sentry from '@sentry/core';

export class PropagaLogger implements LoggerService {
  private levels: LogLevel[] = [];

  log(message: any, ...optionalParams: any[]) {
    console.log([...optionalParams, ...this.levels], message);
  }
  error(message: any, ...optionalParams: any[]) {
    console.error([...optionalParams, ...this.levels], message, process.env.NODE_ENV);
    let context = {};

    if (message && message.config && message.response) {
      const axiosResponse = {
        data: JSON.stringify(message.response.data),
        headers: message.response.headers,
        statusCode: message.response.status,
      };
      const axiosRequest = {
        data: message.config.data,
        headers: JSON.stringify(message.config.headers),
        method: message.config.method,
        url: message.config.url,
      };
      context = {
        'axios request': axiosRequest,
        'axios response': axiosResponse,
      };
    }

    Sentry.captureException(message, {
      contexts: {
        ...context,
        extra: {
          ...optionalParams,
          ...this.levels,
        },
      },
      tags: {
        environment: process.env.NODE_ENV,
        'trace-origin': 'logger',
      },
    });
  }
  warn(message: any, ...optionalParams: any[]) {
    console.warn([...optionalParams, ...this.levels], message);
  }
  debug?(message: any, ...optionalParams: any[]) {
    console.debug([...optionalParams, ...this.levels], message);
  }
  verbose?(message: any, ...optionalParams: any[]) {
    console.debug([...optionalParams, ...this.levels], message);
  }
  setLogLevels?(levels: LogLevel[]) {
    this.levels = [...levels];
  }
}
