import { config } from 'dotenv';
import { Injectable, Logger } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import * as path from 'node:path';

const ENV_PATH = '../../../.env';
const NODE_LOCAL_ENV = 'local';
const NODE_TESTING_ENV = 'testing';
const REGION = 'us-west-2';

@Injectable()
export class ConfigService {
  private logger = new Logger(ConfigService.name);
  private readonly secretName = `${process.env.NODE_ENV}/user-validation/secrets`;
  private readonly envConfig = {};
  private hasEnvironmentVariablesLoaded = false;

  private isLocal = () => [NODE_TESTING_ENV, NODE_LOCAL_ENV].includes(process.env.NODE_ENV);

  public async get(key: string): Promise<string> {
    await this.getManagerFactory();
    return this.envConfig[key];
  }

  private async getManagerFactory(): Promise<void> {
    if (this.isLocal()) {
      this.logger.debug('Retrieve information from local using .env file');
      return this.getLocalEnv();
    }

    if (!this.hasEnvironmentVariablesLoaded) {
      return this.upAWSSecrets();
    }
  }

  private async getLocalEnv(): Promise<void> {
    const localPath = ENV_PATH;
    const envPath = path.join(__dirname, localPath);
    const { parsed } = config({ path: envPath });

    for (const [key, value] of Object.entries(parsed)) {
      this.envConfig[key] = value;
    }

    this.hasEnvironmentVariablesLoaded = true;
  }

  public async upAWSSecrets(): Promise<void> {
    const client = new AWS.SecretsManager({
      region: REGION,
    });

    const secrets = await client.getSecretValue({ SecretId: this.secretName }).promise();

    const resultSecrets = JSON.parse(secrets.SecretString);

    for (const key in resultSecrets) {
      this.envConfig[key] = resultSecrets[key];
    }
    this.hasEnvironmentVariablesLoaded = true;
  }
}
