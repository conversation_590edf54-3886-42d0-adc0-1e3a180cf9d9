import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from './config.service';
import { faker } from '@faker-js/faker';

import * as dotenv from 'dotenv';
jest.mock('dotenv');

describe('ConfigService', () => {
  let service: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConfigService],
    }).compile();

    service = module.get<ConfigService>(ConfigService);
  });

  it('should be get config from local .env file', async () => {
    const key = faker.word.noun();
    process.env.NODE_ENV = 'local';

    (dotenv.config as jest.Mock).mockReturnValue({
      parsed: {
        [key]: 'FAKE_VALUE',
      },
    });

    const response = await service.get(key);
    expect(response).toEqual('FAKE_VALUE');
  });
});
