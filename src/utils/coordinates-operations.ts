interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface GetDistanceBetweenCoordinatesPayload {
  coordinatesA: Coordinates;
  coordinatesB: Coordinates;
}

export class CoordinatesOperations {
  private static degreeToRadius(degree: number) {
    return degree * (Math.PI / 180);
  }

  public static getDistanceBetweenCoordinates({
    coordinatesA,
    coordinatesB,
  }: GetDistanceBetweenCoordinatesPayload) {
    const earthRadius = 6371;

    const distanceLat = this.degreeToRadius(coordinatesB.latitude - coordinatesA.latitude);
    const distanceLon = this.degreeToRadius(coordinatesB.longitude - coordinatesA.longitude);

    const a =
      Math.sin(distanceLat / 2) * Math.sin(distanceLat / 2) +
      Math.cos(this.degreeToRadius(coordinatesA.latitude)) *
        Math.cos(this.degreeToRadius(coordinatesB.latitude)) *
        Math.sin(distanceLon / 2) *
        Math.sin(distanceLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return earthRadius * c * 1000;
  }
}
