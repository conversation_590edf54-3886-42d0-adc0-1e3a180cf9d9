import { Module } from '@nestjs/common';
import { UserLocationController } from './user-location.controller';
import { SaveLocationService } from './use-cases/save-location/save-location.service';
import { MongoDatabaseModule } from 'src/infrastructure/mongo-database/mongo-database.module';

@Module({
  controllers: [UserLocationController],
  providers: [SaveLocationService],
  imports: [MongoDatabaseModule],
})
export class UserLocationModule {}
