import { Injectable } from '@nestjs/common';
import { LocationRepository } from 'src/infrastructure/mongo-database/repositories/location.service';
import { SaveLocationDTO } from '../../dtos/save-location';

@Injectable()
export class SaveLocationService {
  constructor(private readonly locationRepository: LocationRepository) {}

  async handler({ userId, phoneNumber, name, latitude, longitude }: SaveLocationDTO) {
    return this.locationRepository.create({
      latitude: latitude.toString(),
      longitude: longitude.toString(),
      phoneNumber,
      userId,
      name,
    });
  }
}
