import { Test, TestingModule } from '@nestjs/testing';
import { SaveLocationService } from './save-location.service';
import { LocationRepository } from 'src/infrastructure/mongo-database/repositories/location.service';

describe('SaveLocationService', () => {
  let service: SaveLocationService;
  let locationRepository: LocationRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: LocationRepository,
          useValue: {
            create: jest.fn(),
          },
        },
        SaveLocationService,
      ],
    }).compile();

    service = module.get<SaveLocationService>(SaveLocationService);
    locationRepository = module.get<LocationRepository>(LocationRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      userId: '**********',
      phoneNumber: '**********',
      name: '<PERSON>',
      latitude: 123,
      longitude: 123,
    };
    jest.spyOn(locationRepository, 'create').mockResolvedValue(null);

    const result = await service.handler(mockValidName);

    expect(locationRepository.create).toHaveBeenCalledWith({
      latitude: '123',
      longitude: '123',
      phoneNumber: '**********',
      userId: '**********',
      name: 'John Doe',
    });

    expect(result).toBe(null);
  });
});
