import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { SaveLocationDTO } from './dtos/save-location';
import { SaveLocationService } from './use-cases/save-location/save-location.service';
import { SentryInterceptor } from 'src/middleware/error.interceptor';

@UseInterceptors(SentryInterceptor)
@Controller('user-location')
export class UserLocationController {
  constructor(readonly saveLocationService: SaveLocationService) {}

  @Post()
  async saveLocation(@Body() saveLocationDTO: SaveLocationDTO) {
    return this.saveLocationService.handler(saveLocationDTO);
  }
}
