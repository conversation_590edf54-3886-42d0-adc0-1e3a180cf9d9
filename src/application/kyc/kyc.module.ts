import { Modu<PERSON> } from '@nestjs/common';
import { SaveValidationService } from './use-cases/save-validation/save-validation.service';
import { KycController } from './kyc.controller';
import { MongoDatabaseModule } from 'src/infrastructure/mongo-database/mongo-database.module';
import { GetKycValidationService } from './use-cases/get-kyc-validation/get-kyc-validation.service';
import { PropagaMerlinModule } from 'src/infrastructure/propaga-merlin/propaga-merlin.module';

@Module({
  providers: [SaveValidationService, GetKycValidationService],
  controllers: [KycController],
  imports: [MongoDatabaseModule, PropagaMerlinModule],
})
export class KycModule {}
