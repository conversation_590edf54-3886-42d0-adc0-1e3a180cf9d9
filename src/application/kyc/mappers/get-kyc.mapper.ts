import { Kyc } from 'src/infrastructure/mongo-database/models/kyc.entity';

const ScoreByLabel: { [key: string]: number } = {
  'No Threat': 0,
  Suspicious: 50,
  Review: 50,
  'Potential Threat': 100,
  Threat: 100,
};

export class GetKycMapper {
  static cleanOCRInformation(aggregatedData: any) {
    if (!aggregatedData || !aggregatedData.document_ocr?.document_ocr?.information) {
      return {};
    }

    const mapper = ([key, value]) => ({
      [key]: value['text'],
    });

    const reducer = (acc, curr) => ({ ...acc, ...curr });

    return Object.entries(aggregatedData.document_ocr.document_ocr.information)
      .map(mapper)
      .reduce(reducer, {});
  }

  static toResponse(kycResponse: Kyc, translatedReason: string[]) {
    const { label, reason, raw_data: aggregatedData } = kycResponse.dataRaw.data;

    const score = ScoreByLabel[label];

    const enrichment = aggregatedData?.document_ocr?.data_enrichment ?? null;
    const information = GetKycMapper.cleanOCRInformation(aggregatedData);

    return {
      score,
      reason,
      translatedReason,
      enrichment,
      information,
    };
  }
}
