import { Injectable, Logger } from '@nestjs/common';
import { KycRepository } from 'src/infrastructure/mongo-database/repositories/kyc.service';
import { ResponseNotFoundException } from '../../exceptions/response-not-found';
import { GetKycMapper } from '../../mappers/get-kyc.mapper';
import { TextTranslatorService } from 'src/infrastructure/propaga-merlin/use-cases/text-translator/text-translator.service';
import { Kyc } from '../../../../infrastructure/mongo-database/models/kyc.entity';
import { KycReasonsRepository } from '../../../../infrastructure/mongo-database/repositories/kyc-reason.service';

@Injectable()
export class GetKycValidationService {
  logger = new Logger(GetKycValidationService.name);
  constructor(
    private readonly kycRepository: KycRepository,
    private readonly textTranslatorService: TextTranslatorService,
    private readonly kycReasonsRepository: KycReasonsRepository,
  ) {}

  private async handleKycReason(kycResponse: Kyc) {
    const reasons = kycResponse.dataRaw.data.reason as string[];
    const translatedReasons = [];

    for (const reason of reasons) {
      const existingReason = await this.kycReasonsRepository.findKycReasonByReason(reason);
      await this.textTranslatorService.handler(reason);

      if (existingReason) {
        translatedReasons.push(existingReason.translatedReason);
        continue;
      }

      const translatedReason = await this.textTranslatorService.handler(reason);
      await this.kycReasonsRepository.create({
        reason,
        translatedReason: translatedReason.translatedText,
      });

      translatedReasons.push(translatedReason.translatedText);
    }

    return translatedReasons;
  }

  async handler(verificationId: string) {
    const kycResponse = await this.kycRepository.findRequestByVerificationId(verificationId);

    if (!kycResponse) {
      throw new ResponseNotFoundException();
    }

    try {
      const translatedReasons = await this.handleKycReason(kycResponse);
      return GetKycMapper.toResponse(kycResponse, translatedReasons);
    } catch (error) {
      this.logger.error(error);
      throw new ResponseNotFoundException();
    }
  }
}
