import { Test, TestingModule } from '@nestjs/testing';
import { GetKycValidationService } from './get-kyc-validation.service';
import { KycRepository } from 'src/infrastructure/mongo-database/repositories/kyc.service';
import { Kyc } from 'src/infrastructure/mongo-database/models/kyc.entity';
import { TextTranslatorService } from 'src/infrastructure/propaga-merlin/use-cases/text-translator/text-translator.service';
import { KycReasonsRepository } from 'src/infrastructure/mongo-database/repositories/kyc-reason.service';

describe('GetKycValidationService', () => {
  let service: GetKycValidationService;
  let kycRepository: KycRepository;
  let textTranslatorService: TextTranslatorService;
  let kycReasonsRepository: KycReasonsRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: KycRepository,
          useValue: {
            findRequestByVerificationId: jest.fn(),
          },
        },
        {
          provide: TextTranslatorService,
          useValue: {
            handler: jest.fn(),
          },
        },
        {
          provide: KycReasonsRepository,
          useValue: {
            create: jest.fn(),
            findKycReasonByReason: jest.fn(),
          },
        },
        GetKycValidationService,
      ],
    }).compile();

    service = module.get<GetKycValidationService>(GetKycValidationService);
    kycRepository = module.get<KycRepository>(KycRepository);
    textTranslatorService = module.get<TextTranslatorService>(TextTranslatorService);
    kycReasonsRepository = module.get<KycReasonsRepository>(KycReasonsRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const verificationIdMock = '1234567890';
    jest.spyOn(kycRepository, 'findRequestByVerificationId').mockResolvedValue({
      dataRaw: {
        data: {
          reason: ['reason1', 'reason2'],
          label: 'No Threat',
          raw_data: {
            document_ocr: {
              data_enrichment: {},
            },
          },
        },
      },
    } as unknown as Kyc);

    jest.spyOn(textTranslatorService, 'handler').mockResolvedValue({
      originalText: 'reason1',
      score: 100,
      translatedText: 'translatedReason1',
    });

    jest.spyOn(kycReasonsRepository, 'findKycReasonByReason').mockResolvedValue(null);
    jest.spyOn(kycReasonsRepository, 'create').mockResolvedValue(null);

    const result = await service.handler(verificationIdMock);

    expect(result).toEqual({
      score: 0,
      reason: ['reason1', 'reason2'],
      translatedReason: ['translatedReason1', 'translatedReason1'],
      enrichment: {},
      information: {},
    });
  });
});
