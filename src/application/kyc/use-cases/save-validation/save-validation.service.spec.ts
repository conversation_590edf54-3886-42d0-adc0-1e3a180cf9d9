import { Test, TestingModule } from '@nestjs/testing';
import { SaveValidationService } from './save-validation.service';
import { KycRepository } from 'src/infrastructure/mongo-database/repositories/kyc.service';

describe('SaveValidationService', () => {
  let service: SaveValidationService;
  let kycRepository: KycRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: KycRepository,
          useValue: {
            create: jest.fn(),
          },
        },
        SaveValidationService,
      ],
    }).compile();

    service = module.get<SaveValidationService>(SaveValidationService);
    kycRepository = module.get<KycRepository>(KycRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      verificationId: '**********',
      dataRaw: {},
      userId: '**********',
      requestId: '**********',
    };
    jest.spyOn(kycRepository, 'create').mockResolvedValue(null);

    const result = await service.handler(mockValidName);

    expect(kycRepository.create).toHaveBeenCalledWith(mockValidName);

    expect(result).toEqual(null);
  });
});
