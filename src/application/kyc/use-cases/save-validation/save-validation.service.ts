import { Injectable } from '@nestjs/common';
import { KycRepository } from 'src/infrastructure/mongo-database/repositories/kyc.service';

@Injectable()
export class SaveValidationService {
  constructor(private readonly kycRepository: KycRepository) {}

  async handler({
    verificationId,
    dataRaw,
    userId,
    requestId,
  }: {
    verificationId: string;
    dataRaw: object;
    userId: string;
    requestId: string;
  }) {
    return this.kycRepository.create({ verificationId, dataRaw, userId, requestId });
  }
}
