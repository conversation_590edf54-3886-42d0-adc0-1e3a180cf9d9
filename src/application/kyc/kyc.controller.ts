import { Body, Controller, Get, Post, Query, UseInterceptors } from '@nestjs/common';
import { SaveValidationService } from './use-cases/save-validation/save-validation.service';
import { SaveValidationDTO } from './dtos/save-validation';
import { GetKycValidationService } from './use-cases/get-kyc-validation/get-kyc-validation.service';
import { SentryInterceptor } from 'src/middleware/error.interceptor';

@UseInterceptors(SentryInterceptor)
@Controller('kyc')
export class KycController {
  constructor(
    readonly saveValidationService: SaveValidationService,
    readonly getKycValidationService: GetKycValidationService,
  ) {}

  @Post('/validation')
  async saveValidation(@Body() saveValidationDTO: SaveValidationDTO) {
    return this.saveValidationService.handler(saveValidationDTO);
  }

  @Get('/validation')
  async getValidation(@Query('verificationId') verificationId: string) {
    return this.getKycValidationService.handler(verificationId);
  }
}
