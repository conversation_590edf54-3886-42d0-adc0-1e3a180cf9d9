export const ErrorCodes = Object.freeze({
  INVALID_DISTANCE_BETWEEN_STORES: {
    code: 'INVALID_DISTANCE_BETWEEN_STORES',
    message: 'Two stores are too close from each other',
  },
  INVALID_PHONE_NUMBER: {
    code: 'INVALID_PHONE_NUMBER',
    message: 'Phone number not valid or inexisting',
  },
  CONTACT_INVALID_PHONE_NUMBER: {
    code: 'CONTACT_INVALID_PHONE_NUMBER',
    message: 'Phone number not valid or inexisting',
  },
  RESPONSE_NOT_FOUND: {
    code: 'RESPONSE_NOT_FOUND',
    message: 'Response not found',
  },
  CONTACT_INVALID_WHATSAPP: {
    code: 'CONTACT_INVALID_WHATSAPP',
    message: 'This number doesnt have a whatsapp account',
  },
  CONTACT_INVALID: {
    code: 'CONTACT_INVALID',
    message: 'Invalid contact',
  },
  WHATSAPP_NOT_EXISTS: {
    code: 'WHATSAPP_NOT_EXISTS',
    message: 'This number doesnt have a whatsapp account',
  },
  UNKNOWN_ERROR: {
    code: 'UNKNOWN_ERROR',
  },
  PHONE_NUMBER_IS_NOT_ACTIVE: {
    code: 'PHONE_NUMBER_IS_NOT_ACTIVE',
    message: 'This number is not active',
  },
  CONTACT_IS_AUTORREFERED: {
    code: 'CONTACT_IS_AUTORREFERED',
    message: 'This number is autorreferred',
  },
  INVALID_NAME: {
    code: 'INVALID_NAME',
    message: 'Name is not valid',
  },
  INVALID_DISTANCE_BETWEEN_STORE_IN_DEFAULT: {
    code: 'INVALID_DISTANCE_BETWEEN_STORE_IN_DEFAULT',
    message: 'The store is too close to another store in default',
  },
  CONTACT_ALREADY_REGISTERED_IN_DEFAULT: {
    code: 'CONTACT_ALREADY_REGISTERED_IN_DEFAULT',
    message: 'Contact already registered and is in default',
  },
  CONTACT_ALREADY_REGISTERED: {
    code: 'CONTACT_ALREADY_REGISTERED',
    message: 'Contact already registered',
  },
  CONTACT_INVALID_NAME: {
    code: 'CONTACT_INVALID_NAME',
    message: 'Name is not valid',
  },
  USER_IN_DEFAULT_WITH_ANOTHER_WHOLESALER: {
    code: 'USER_IN_DEFAULT_WITH_ANOTHER_WHOLESALER',
    message: 'This user is in default with another wholesaler',
  },
  USER_IS_UNVERIFIED_BY_WHOLESALER: {
    code: 'USER_IS_UNVERIFIED_BY_WHOLESALER',
    message: 'This user is unverified by the wholesaler',
  },
  CONTACT_BLACKLISTED_NUMBER: {
    code: 'CONTACT_BLACKLISTED_NUMBER',
    message: 'This contact has a blacklisted number',
  },
  PHONE_NUMBER_IS_BLACKLISTED: {
    code: 'PHONE_NUMBER_IS_BLACKLISTED',
    message: 'This number is blacklisted',
  },
});

export const MIN_DISTANCE_BETWEEN_STORES_IN_METERS = 10;
export const EMPTY_SPACE = ' ';
export const COUNTRY_CODE = '+52';
export const COUNTRY_CODE_NUMBER = '52';
export const ZERO = 0;

export const DuplicateUserScores = Object.freeze({
  MAX_VALUE: 100,
  OK_VALUE: 0,
});

export const PropagaMerlinScores = Object.freeze({
  MIN_ACCEPTED_SCORE: 60,
});

export const UserStatus = Object.freeze({
  IN_PAYMENT_DEFAULT: 'in-payment-default',
  IN_PAYMENT_PLAN: 'in-payment-plan',
  VALIDATED: 'validated',
  VALIDATED_BY_WHOLESALER: 'validated-by-wholesaler',
  PENDING_VALIDATION: 'pending-validation',
  ON_KYC_VERIFICATION: 'on-kyc-verification',
  MANUAL_VALIDATION: 'manual-validation',
  MERGED: 'merged',
});

export const ValidationNames = Object.freeze({
  NAME_VALIDATION: 'nameValidation',
  CONTACT_NAME_VALIDATION: 'contactNameValidation',
  PHONE_NUMBER_VALIDATION: 'phoneNumberValidation',
  WHATSAPP_VALIDATION: 'whatsappValidation',
  GEOLOCATION_VALIDATION: 'geolocationValidation',
  AUTO_REFERED_VALIDATION: 'autoReferedValidation',
  REGISTRATION_VALIDATION: 'registrationValidation',
  METADATA_VALIDATION: 'metadataValidation',
});
