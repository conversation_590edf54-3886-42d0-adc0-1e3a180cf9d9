import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { PropagaDatabaseModule } from 'src/infrastructure/propaga-database/propaga-database.module';
import { PropagaMerlinModule } from 'src/infrastructure/propaga-merlin/propaga-merlin.module';
import { ValidateGeolocationService } from './use-cases/validate-geolocation/validate-geolocation.service';
import { UserValidationStrategyService } from './use-cases/user-validation-strategy/user-validation-strategy.service';
import { MongoDatabaseModule } from 'src/infrastructure/mongo-database/mongo-database.module';
import { ValidateNameService } from './use-cases/validate-name/validate-name.service';
import { ValidateMetadataService } from './use-cases/validate-metadata/validate-metadata.service';
import { ValidatePhoneNumberService } from './use-cases/validate-phone-number/validate-phone-number.service';

@Module({
  controllers: [],
  providers: [
    ValidateGeolocationService,
    UserValidationStrategyService,
    ValidateNameService,
    ValidateMetadataService,
    ValidatePhoneNumberService,
  ],
  imports: [PropagaMerlinModule, PropagaDatabaseModule, HttpModule, MongoDatabaseModule],
  exports: [ValidateGeolocationService, UserValidationStrategyService],
})
export class UserValidationModule {}
