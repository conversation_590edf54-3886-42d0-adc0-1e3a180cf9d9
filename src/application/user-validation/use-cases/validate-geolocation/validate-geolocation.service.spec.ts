import { Test, TestingModule } from '@nestjs/testing';
import { ValidateGeolocationService } from './validate-geolocation.service';
import { UserRepository } from 'src/infrastructure/propaga-database/repositories/user.repository';
import { LocationRepository } from 'src/infrastructure/mongo-database/repositories/location.service';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';

describe('ValidateGeolocationMatchService', () => {
  let service: ValidateGeolocationService;
  let userRepository: UserRepository;
  let locationRepository: LocationRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: UserRepository,
          useValue: {
            findUserByPhoneNumber: jest.fn(),
          },
        },
        {
          provide: LocationRepository,
          useValue: {
            findAll: jest.fn(),
          },
        },
        ValidateGeolocationService,
      ],
    }).compile();

    service = module.get<ValidateGeolocationService>(ValidateGeolocationService);
    userRepository = module.get<UserRepository>(UserRepository);
    locationRepository = module.get<LocationRepository>(LocationRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid geolocation', async () => {
    jest.spyOn(userRepository, 'findUserByPhoneNumber').mockResolvedValue(null);
    jest.spyOn(locationRepository, 'findAll').mockResolvedValue([]);

    const result = await service.validate({
      phoneNumber: '**********',
      latitude: '123',
      longitude: '123',
    } as ValidateUserPayload);

    expect(result).toEqual({
      validation: 'geolocationValidation',
      score: 0,
    });
  });
});
