import { Injectable, Logger } from '@nestjs/common';
import {
  EMPTY_SPACE,
  ErrorCodes,
  MIN_DISTANCE_BETWEEN_STORES_IN_METERS,
  UserStatus,
  ValidationNames,
} from 'src/application/constants';
import { UserFraudRiskContext } from 'src/application/score/interfaces/user-fraud-risk-response';
import { UserRepository } from 'src/infrastructure/propaga-database/repositories/user.repository';
import { LocationRepository } from 'src/infrastructure/mongo-database/repositories/location.service';
import { CoordinatesOperations } from 'src/utils/coordinates-operations';
import { UserFraudRiskValidatorStrategy } from 'src/application/score/interfaces/user-fraud-risk-strategy.interface';
import { Scores } from 'src/application/score/constants';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';
import { FraudRiskContext } from 'src/interfaces/fraud-risk-context.interface';

@Injectable()
export class ValidateGeolocationService implements UserFraudRiskValidatorStrategy {
  logger = new Logger(ValidateGeolocationService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly locationRepository: LocationRepository,
  ) {}

  private async validateGeolocationMatch(
    matchedUserId: string,
    newPhoneNumber: string,
    { geolocationValidation }: FraudRiskContext['userValidatorRules'],
  ) {
    const user = await this.userRepository.findUserStatusById(matchedUserId);

    if (!user) {
      return {
        validation: ValidationNames.GEOLOCATION_VALIDATION,
        score: Scores.REVIEW_VALUE,
        errorCode: ErrorCodes.INVALID_DISTANCE_BETWEEN_STORES.code,
        errorMessage: ErrorCodes.INVALID_DISTANCE_BETWEEN_STORES.message,
        errorValue: matchedUserId,
      };
    }

    const errorValue = `${user.firstName} ${user.lastNames || EMPTY_SPACE} (${user.phoneNumber})`;
    const isSameUser = user.phoneNumber === newPhoneNumber;

    const isUserInDefault = [
      UserStatus.IN_PAYMENT_DEFAULT.toString(),
      UserStatus.IN_PAYMENT_PLAN.toString(),
    ].includes(user.status.name);

    if (isSameUser && !isUserInDefault) {
      return {
        validation: ValidationNames.GEOLOCATION_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    if (isSameUser && isUserInDefault) {
      return {
        validation: ValidationNames.GEOLOCATION_VALIDATION,
        score: Scores.MAX_VALUE,
        errorCode: ErrorCodes.USER_IN_DEFAULT_WITH_ANOTHER_WHOLESALER.code,
        errorMessage: ErrorCodes.USER_IN_DEFAULT_WITH_ANOTHER_WHOLESALER.message,
        errorValue,
      };
    }

    if (isUserInDefault && geolocationValidation.userIsInPaymentDefault) {
      return {
        validation: ValidationNames.GEOLOCATION_VALIDATION,
        score: Scores.MAX_VALUE,
        errorCode: ErrorCodes.INVALID_DISTANCE_BETWEEN_STORE_IN_DEFAULT.code,
        errorMessage: ErrorCodes.INVALID_DISTANCE_BETWEEN_STORE_IN_DEFAULT.message,
        errorValue,
      };
    }

    if (!geolocationValidation.userIsNotInPaymentDefault) {
      return {
        validation: ValidationNames.GEOLOCATION_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    return {
      validation: ValidationNames.GEOLOCATION_VALIDATION,
      score: Scores.REVIEW_VALUE,
      errorCode: ErrorCodes.INVALID_DISTANCE_BETWEEN_STORES.code,
      errorMessage: ErrorCodes.INVALID_DISTANCE_BETWEEN_STORES.message,
      errorValue,
    };
  }

  public async validate(
    { phoneNumber, latitude, longitude }: ValidateUserPayload,
    { userValidatorRules }: FraudRiskContext,
  ): Promise<UserFraudRiskContext> {
    const userCoordinates = { latitude: Number(latitude), longitude: Number(longitude) };
    const coordinates = await this.locationRepository.findAll();

    let matchedUser = null;

    coordinates.some((coordinate) => {
      const distanceInMeters = CoordinatesOperations.getDistanceBetweenCoordinates({
        coordinatesA: userCoordinates,
        coordinatesB: {
          latitude: Number(coordinate.latitude),
          longitude: Number(coordinate.longitude),
        },
      });

      if (distanceInMeters < MIN_DISTANCE_BETWEEN_STORES_IN_METERS) {
        this.logger.log(
          `Invalid distance (${distanceInMeters.toFixed(2)} m) with userId ${coordinate.userId} (${
            coordinate.phoneNumber
          })`,
        );

        matchedUser = coordinate;
        return true;
      }
      return false;
    });

    if (!matchedUser) {
      return {
        validation: ValidationNames.GEOLOCATION_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    return this.validateGeolocationMatch(matchedUser.userId, phoneNumber, userValidatorRules);
  }
}
