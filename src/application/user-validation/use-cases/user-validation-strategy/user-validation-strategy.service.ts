import { Injectable } from '@nestjs/common';
import { UserFraudRiskResult } from 'src/application/score/interfaces/user-fraud-risk-response';
import { ValidateGeolocationService } from '../validate-geolocation/validate-geolocation.service';
import { ValidateNameService } from '../validate-name/validate-name.service';
import { FraudRiskValidatorsStrategy } from 'src/application/score/interfaces/user-fraud-risk-strategy.interface';
import { ValidateMetadataService } from '../validate-metadata/validate-metadata.service';
import { UserPayloadDTO } from 'src/application/score/dtos/validate-user';
import { ValidatePhoneNumberService } from '../validate-phone-number/validate-phone-number.service';
import { FraudRiskContext } from 'src/interfaces/fraud-risk-context.interface';

@Injectable()
export class UserValidationStrategyService implements FraudRiskValidatorsStrategy {
  constructor(
    private readonly validateNameService: ValidateNameService,
    private readonly validateGeolication: ValidateGeolocationService,
    private readonly validateMetadata: ValidateMetadataService,
    private readonly validatePhoneNumber: ValidatePhoneNumberService,
  ) {}

  public async validate(
    payload: UserPayloadDTO,
    context: FraudRiskContext,
  ): Promise<UserFraudRiskResult> {
    const validators = [
      this.validateGeolication.validate(payload, context),
      this.validateNameService.validate(payload, context),
      this.validateMetadata.validate(payload),
      this.validatePhoneNumber.validate(payload),
    ];

    const validatorsResponse = await Promise.allSettled(validators);
    const results = validatorsResponse.map((response) => response['value']);
    const response = results.filter((result) => !!result).flat();

    return {
      validator: 'userValidator',
      isValid: response.every((result) => result.score !== 0),
      score: response.reduce((max, current) => {
        if (current && current.score) {
          return Math.max(max, current.score);
        }
        return Math.max(max, 0);
      }, Number.MIN_SAFE_INTEGER),
      context: response,
    };
  }
}
