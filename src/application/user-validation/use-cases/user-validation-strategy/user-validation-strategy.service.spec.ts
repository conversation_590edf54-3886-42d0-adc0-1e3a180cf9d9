import { Test, TestingModule } from '@nestjs/testing';
import { UserValidationStrategyService } from './user-validation-strategy.service';
import { ValidateNameService } from '../validate-name/validate-name.service';
import { ValidateGeolocationService } from '../validate-geolocation/validate-geolocation.service';
import { ValidateMetadataService } from '../validate-metadata/validate-metadata.service';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';
import { ValidatePhoneNumberService } from '../validate-phone-number/validate-phone-number.service';

describe('UserValidationStrategyService', () => {
  let service: UserValidationStrategyService;
  let validateNameService: ValidateNameService;
  let validateGeolication: ValidateGeolocationService;
  let validateMetadata: ValidateMetadataService;
  let validatePhoneNumberService: ValidatePhoneNumberService;
  let phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ValidateNameService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ValidateGeolocationService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ValidateMetadataService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ValidatePhoneNumberService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: PhoneNumbersBlacklistRepository,
          useValue: {
            findByPhoneNumber: jest.fn(),
          },
        },
        UserValidationStrategyService,
      ],
    }).compile();

    service = module.get<UserValidationStrategyService>(UserValidationStrategyService);
    validateNameService = module.get<ValidateNameService>(ValidateNameService);
    validateGeolication = module.get<ValidateGeolocationService>(ValidateGeolocationService);
    validateMetadata = module.get<ValidateMetadataService>(ValidateMetadataService);
    phoneNumbersBlacklistRepository = module.get<PhoneNumbersBlacklistRepository>(
      PhoneNumbersBlacklistRepository,
    );
    validatePhoneNumberService = module.get<ValidatePhoneNumberService>(ValidatePhoneNumberService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '1234567890',
      contacts: [
        {
          fullName: 'John Doe',
          phoneNumber: '1234567890',
        },
      ],
    };
    jest.spyOn(validateNameService, 'validate').mockResolvedValue({
      validation: 'nameValidation',
      score: 100,
    });
    jest.spyOn(validateGeolication, 'validate').mockResolvedValue({
      validation: 'geolocationValidation',
      score: 100,
    });

    jest.spyOn(validateMetadata, 'validate').mockResolvedValue({
      validation: 'metadataValidation',
      score: 100,
    });

    jest.spyOn(validatePhoneNumberService, 'validate').mockResolvedValue({
      validation: 'phoneNumberValidation',
      score: 100,
    });

    jest.spyOn(phoneNumbersBlacklistRepository, 'findByPhoneNumber').mockResolvedValue(null);

    const result = await service.validate(mockValidName);

    expect(result).toEqual({
      validator: 'userValidator',
      isValid: true,
      score: 100,
      context: expect.arrayContaining([
        {
          validation: 'nameValidation',
          score: 100,
        },
        {
          validation: 'geolocationValidation',
          score: 100,
        },
        {
          validation: 'phoneNumberValidation',
          score: 100,
        },
      ]),
    });
  });
});
