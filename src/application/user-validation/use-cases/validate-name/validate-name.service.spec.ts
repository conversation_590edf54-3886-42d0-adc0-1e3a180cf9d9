import { Test, TestingModule } from '@nestjs/testing';
import { ValidateNameService } from './validate-name.service';
import { NameValidationService } from 'src/infrastructure/propaga-merlin/use-cases/name-validation/name-validation.service';
import { ErrorCodes, ValidationNames } from 'src/application/constants';
import { Scores } from 'src/application/score/constants';

describe('ValidateNameService (unit-tests)', () => {
  let validateNameService: ValidateNameService;
  let nameValidationService: NameValidationService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: NameValidationService,
          useValue: {
            handler: jest.fn(),
          },
        },
        ValidateNameService,
      ],
    }).compile();
    validateNameService = moduleFixture.get<ValidateNameService>(ValidateNameService);
    nameValidationService = moduleFixture.get<NameValidationService>(NameValidationService);
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [],
    };
    jest
      .spyOn(nameValidationService, 'handler')
      .mockResolvedValue({ isValid: true, score: 100, context: '' });

    const result = await validateNameService.validate(mockValidName);

    expect(result).toEqual({
      validation: ValidationNames.NAME_VALIDATION,
      score: Scores.OK_VALUE,
    });
  });

  it('should return error for an invalid name', async () => {
    const mockInvalidName = {
      firstName: 'Invalid123',
      lastNames: 'Name456',
      phoneNumber: '**********',
      contacts: [],
    };
    jest.spyOn(nameValidationService, 'handler').mockResolvedValue({
      isValid: false,
      score: 0,
      context: 'Invalid name format',
    });

    const result = await validateNameService.validate(mockInvalidName);

    expect(result).toEqual({
      validation: ValidationNames.NAME_VALIDATION,
      errorCode: ErrorCodes.INVALID_NAME.code,
      errorMessage: 'Invalid name format',
      score: 0,
      errorValue: 'Invalid123 Name456',
    });
  });

  it('should return review score for a name that needs review', async () => {
    const mockReviewName = {
      firstName: 'Review',
      lastNames: 'Name',
      phoneNumber: '**********',
      contacts: [],
    };
    jest.spyOn(nameValidationService, 'handler').mockResolvedValue({
      isValid: false,
      score: 50,
      context: 'Name needs review',
    });

    const result = await validateNameService.validate(mockReviewName);

    expect(result).toEqual({
      validation: ValidationNames.NAME_VALIDATION,
      errorCode: ErrorCodes.INVALID_NAME.code,
      errorMessage: 'Name needs review',
      score: Scores.REVIEW_VALUE,
      errorValue: 'Review Name',
    });
  });

  it('should return max score for a name that is valid', async () => {
    const mockValidNameRequest = {
      firstName: 'Valid',
      lastNames: 'Name',
      phoneNumber: '**********',
      contacts: [],
    };

    jest.spyOn(nameValidationService, 'handler').mockResolvedValue({
      isValid: false,
      score: 0,
      context: 'Name is valid',
    });

    const result = await validateNameService.validate(mockValidNameRequest);

    expect(result).toEqual({
      validation: ValidationNames.NAME_VALIDATION,
      score: Scores.OK_VALUE,
      errorCode: 'INVALID_NAME',
      errorMessage: 'Name is valid',
      errorValue: 'Valid Name',
    });
  });
});
