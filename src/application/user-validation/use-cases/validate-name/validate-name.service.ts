import { Injectable } from '@nestjs/common';
import { EMPTY_SPACE, ErrorCodes, ValidationNames } from 'src/application/constants';
import { Scores } from 'src/application/score/constants';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';
import { FraudRiskContext } from 'src/interfaces/fraud-risk-context.interface';
import { UserFraudRiskContext } from 'src/application/score/interfaces/user-fraud-risk-response';
import { UserFraudRiskValidatorStrategy } from 'src/application/score/interfaces/user-fraud-risk-strategy.interface';
import { NameValidationService } from 'src/infrastructure/propaga-merlin/use-cases/name-validation/name-validation.service';

@Injectable()
export class ValidateNameService implements UserFraudRiskValidatorStrategy {
  constructor(private readonly nameValidationService: NameValidationService) {}

  async validate(
    { firstName, lastNames }: ValidateUserPayload,
    { userValidatorRules }: FraudRiskContext,
  ): Promise<UserFraudRiskContext> {
    if (!userValidatorRules.nameValidation.nameIsNotValid) {
      return {
        validation: ValidationNames.NAME_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    const userName = lastNames ? firstName + EMPTY_SPACE + lastNames : firstName;

    const nameValidationResult = await this.nameValidationService.handler(userName);
    if (nameValidationResult.isValid) {
      return {
        validation: ValidationNames.NAME_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    const validationScore =
      nameValidationResult.score > 0 && nameValidationResult.score < 100
        ? Scores.REVIEW_VALUE
        : nameValidationResult.score;

    return {
      validation: ValidationNames.NAME_VALIDATION,
      errorCode: ErrorCodes.INVALID_NAME.code,
      errorMessage: nameValidationResult.context,
      score: validationScore,
      errorValue: userName,
    };
  }
}
