import { Test, TestingModule } from '@nestjs/testing';
import { ValidatePhoneNumberService } from './validate-phone-number.service';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';
import { faker } from '@faker-js/faker/.';
import { Types } from 'mongoose';

describe('ValidatePhoneNumberService', () => {
  let service: ValidatePhoneNumberService;
  let phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PhoneNumbersBlacklistRepository,
          useValue: {
            findByPhoneNumber: jest.fn(),
          },
        },
        ValidatePhoneNumberService,
      ],
    }).compile();

    service = module.get<ValidatePhoneNumberService>(ValidatePhoneNumberService);
    phoneNumbersBlacklistRepository = module.get<PhoneNumbersBlacklistRepository>(
      PhoneNumbersBlacklistRepository,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a non blacklisted phoneNumber', async () => {
    const phoneNumber = faker.phone.number();
    const mockValidPhoneNumber = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber,
      contacts: [],
    };
    jest.spyOn(phoneNumbersBlacklistRepository, 'findByPhoneNumber').mockResolvedValue(null);

    const result = await service.validate(mockValidPhoneNumber);

    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toBeCalledTimes(1);
    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toHaveBeenCalledWith(phoneNumber);

    expect(result).toStrictEqual({
      validation: 'phoneNumberValidation',
      score: 0,
    });
  });

  it('should validate a blacklisted phoneNumber', async () => {
    const phoneNumber = faker.phone.number();
    const reason = faker.lorem.text();
    const mockValidPhoneNumber = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber,
      contacts: [],
    };

    jest.spyOn(phoneNumbersBlacklistRepository, 'findByPhoneNumber').mockResolvedValue({
      _id: new Types.ObjectId(),
      phoneNumber,
      reason,
      save: jest.fn(),
      toObject: jest.fn().mockReturnValue({
        _id: new Types.ObjectId(),
        phoneNumber,
        reason,
      }),
      $isDeleted: jest.fn(),
      $isModified: jest.fn(),
      $isNew: jest.fn(),
      $clone: jest.fn(),
    } as any);

    const result = await service.validate(mockValidPhoneNumber);

    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toBeCalledTimes(1);
    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toHaveBeenCalledWith(phoneNumber);
    expect(result).toEqual({
      validation: 'phoneNumberValidation',
      errorCode: 'PHONE_NUMBER_IS_BLACKLISTED',
      errorMessage: 'This number is blacklisted',
      score: 100,
      errorValue: phoneNumber,
    });
  });
});
