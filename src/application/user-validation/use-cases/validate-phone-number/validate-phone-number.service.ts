import { Injectable } from '@nestjs/common';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';
import { UserFraudRiskContext } from 'src/application/score/interfaces/user-fraud-risk-response';
import { UserFraudRiskValidatorStrategy } from 'src/application/score/interfaces/user-fraud-risk-strategy.interface';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';
import { Scores } from 'src/application/score/constants';
import { ErrorCodes, ValidationNames } from 'src/application/constants';

@Injectable()
export class ValidatePhoneNumberService implements UserFraudRiskValidatorStrategy {
  constructor(private readonly phoneNumberBlacklistRepository: PhoneNumbersBlacklistRepository) {}

  private async validateBlacklistedPhoneNumber(phoneNumber: string) {
    const isBlacklisted = await this.phoneNumberBlacklistRepository.findByPhoneNumber(phoneNumber);

    if (!isBlacklisted) {
      return {
        validation: ValidationNames.PHONE_NUMBER_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    return {
      validation: ValidationNames.PHONE_NUMBER_VALIDATION,
      errorCode: ErrorCodes.PHONE_NUMBER_IS_BLACKLISTED.code,
      errorMessage: ErrorCodes.PHONE_NUMBER_IS_BLACKLISTED.message,
      score: Scores.MAX_VALUE,
      errorValue: phoneNumber,
    };
  }

  async validate({ phoneNumber }: ValidateUserPayload): Promise<UserFraudRiskContext> {
    return this.validateBlacklistedPhoneNumber(phoneNumber);
  }
}
