import { Injectable } from '@nestjs/common';
import { ErrorCodes, ValidationNames } from 'src/application/constants';
import { Scores } from 'src/application/score/constants';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';
import { UserFraudRiskContext } from 'src/application/score/interfaces/user-fraud-risk-response';
import { UserFraudRiskValidatorStrategy } from 'src/application/score/interfaces/user-fraud-risk-strategy.interface';

@Injectable()
export class ValidateMetadataService implements UserFraudRiskValidatorStrategy {
  async validate({ metadata }: ValidateUserPayload): Promise<UserFraudRiskContext> {
    const okResponse = {
      validation: ValidationNames.METADATA_VALIDATION,
      score: Scores.OK_VALUE,
    };

    if (!metadata) {
      return okResponse;
    }

    if (Boolean(metadata['unverified'])) {
      return {
        validation: ValidationNames.METADATA_VALIDATION,
        score: Scores.MAX_VALUE,
        errorCode: ErrorCodes.USER_IS_UNVERIFIED_BY_WHOLESALER.code,
        errorMessage: ErrorCodes.USER_IS_UNVERIFIED_BY_WHOLESALER.message,
        errorValue: 'unverified',
      };
    }

    return okResponse;
  }
}
