import { Test, TestingModule } from '@nestjs/testing';
import { ValidateMetadataService } from './validate-metadata.service';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';
import { ValidationNames } from 'src/application/constants';

describe('ValidateMetadataService', () => {
  let validateMetadataService: ValidateMetadataService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidateMetadataService],
    }).compile();

    validateMetadataService = module.get<ValidateMetadataService>(ValidateMetadataService);
  });

  it('should be defined', () => {
    expect(validateMetadataService).toBeDefined();
  });

  const basePayload: ValidateUserPayload = {
    metadata: {},
    phoneNumber: '',
    firstName: '',
    lastNames: '',
    contacts: [],
  };

  it('should return a valid result when metadata is not provided', async () => {
    const result = await validateMetadataService.validate(basePayload);
    expect(result).toEqual({
      validation: ValidationNames.METADATA_VALIDATION,
      score: 0,
    });
  });

  it('should return an invalid result when metadata flag unverified is provided', async () => {
    const result = await validateMetadataService.validate({
      ...basePayload,
      metadata: {
        unverified: true,
      },
    });

    expect(result).toEqual({
      validation: ValidationNames.METADATA_VALIDATION,
      score: 100,
      errorCode: 'USER_IS_UNVERIFIED_BY_WHOLESALER',
      errorMessage: 'This user is unverified by the wholesaler',
      errorValue: 'unverified',
    });
  });

  it('should return a valid result when metadata flag unverified is false', async () => {
    const result = await validateMetadataService.validate({
      ...basePayload,
      metadata: {
        unverified: false,
      },
    });

    expect(result).toEqual({
      validation: ValidationNames.METADATA_VALIDATION,
      score: 0,
    });
  });
});
