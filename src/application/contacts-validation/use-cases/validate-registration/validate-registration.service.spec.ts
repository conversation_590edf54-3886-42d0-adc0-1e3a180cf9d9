import { Test, TestingModule } from '@nestjs/testing';
import { ValidateRegistrationService } from './validate-registration.service';
import { UserRepository } from 'src/infrastructure/propaga-database/repositories/user.repository';
import { Users } from 'src/infrastructure/propaga-database/models/users.entity';

describe('ValidateRegistrationService', () => {
  let service: ValidateRegistrationService;
  let userRepository: UserRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: UserRepository,
          useValue: {
            findUsersStatusByPhoneNumber: jest.fn(),
          },
        },
        ValidateRegistrationService,
      ],
    }).compile();

    service = module.get<ValidateRegistrationService>(ValidateRegistrationService);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [],
    };
    jest.spyOn(userRepository, 'findUsersStatusByPhoneNumber').mockResolvedValue([] as Users[]);

    const result = await service.validate(mockValidName);

    expect(result).toEqual([
      {
        validation: 'registrationValidation',
        score: 0,
      },
    ]);
  });

  it('should validate a autoreferred contact', async () => {
    const mockAutoreferredContact = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [{ fullName: 'John Doe', phoneNumber: '**********' }],
    };

    jest.spyOn(userRepository, 'findUsersStatusByPhoneNumber').mockResolvedValue([
      {
        firstName: 'John',
        lastNames: 'Doe',
        phoneNumber: '**********',
      },
    ] as Users[]);

    const result = await service.validate(mockAutoreferredContact);

    expect(result).toEqual([
      {
        validation: 'registrationValidation',
        score: 100,
        errorCode: 'CONTACT_IS_AUTORREFERED',
        errorMessage: 'This number is autorreferred',
        errorValue: 'John Doe (**********)',
      },
    ]);
  });

  it('should validate a contact already registered', async () => {
    const mockExistingContact = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '5634363878',
      contacts: [{ fullName: 'Jon Snow', phoneNumber: '**********' }],
    };

    jest.spyOn(userRepository, 'findUsersStatusByPhoneNumber').mockResolvedValue([
      {
        firstName: 'Jon',
        lastNames: 'Snow',
        phoneNumber: '**********',
        status: {
          name: 'validated',
        },
      },
    ] as Users[]);

    const result = await service.validate(mockExistingContact);

    expect(result).toEqual([
      {
        validation: 'registrationValidation',
        score: 50,
        errorCode: 'CONTACT_ALREADY_REGISTERED',
        errorMessage: 'Contact already registered',
        errorValue: 'Jon Snow (**********)',
      },
    ]);
  });

  it('should validate a contact already registered and is in default', async () => {
    const mockExistingContact = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '5634363878',
      contacts: [{ fullName: 'Jon Snow', phoneNumber: '**********' }],
    };

    jest.spyOn(userRepository, 'findUsersStatusByPhoneNumber').mockResolvedValue([
      {
        firstName: 'Jon',
        lastNames: 'Snow',
        phoneNumber: '**********',
        status: {
          name: 'in-payment-default',
        },
      },
    ] as Users[]);

    const result = await service.validate(mockExistingContact);

    expect(result).toEqual([
      {
        validation: 'registrationValidation',
        score: 100,
        errorCode: 'CONTACT_ALREADY_REGISTERED_IN_DEFAULT',
        errorMessage: 'Contact already registered and is in default',
        errorValue: 'Jon Snow (**********)',
      },
    ]);
  });
});
