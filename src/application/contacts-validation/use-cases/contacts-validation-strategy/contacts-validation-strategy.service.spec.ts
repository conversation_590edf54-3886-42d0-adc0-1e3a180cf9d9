import { Test, TestingModule } from '@nestjs/testing';
import { ContactsValidationStrategyService } from './contacts-validation-strategy.service';
import { ValidateRegistrationService } from '../validate-registration/validate-registration.service';
import { ValidatePhoneNumbersService } from '../validate-phone-numbers/validate-phone-numbers.service';
import { ValidateNamesService } from '../validate-names/validate-names.service';

describe('ContactsValidationStrategyService', () => {
  let service: ContactsValidationStrategyService;
  let validateRegistrationService: ValidateRegistrationService;
  let validatePhoneNumbersService: ValidatePhoneNumbersService;
  let validateNamesService: ValidateNamesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ValidateRegistrationService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ValidatePhoneNumbersService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ValidateNamesService,
          useValue: {
            validate: jest.fn(),
          },
        },
        ContactsValidationStrategyService,
      ],
    }).compile();

    service = module.get<ContactsValidationStrategyService>(ContactsValidationStrategyService);
    validateRegistrationService = module.get<ValidateRegistrationService>(
      ValidateRegistrationService,
    );
    validatePhoneNumbersService = module.get<ValidatePhoneNumbersService>(
      ValidatePhoneNumbersService,
    );
    validateNamesService = module.get<ValidateNamesService>(ValidateNamesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should cleaned contacts', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '1234567890',
      contacts: [
        {
          fullName: 'John Doe',
          phoneNumber: '123 456  78 9 0',
        },
      ],
    };
    jest.spyOn(validateRegistrationService, 'validate').mockResolvedValue([
      {
        validation: 'registration',
        score: 100,
      },
    ]);
    jest.spyOn(validatePhoneNumbersService, 'validate').mockResolvedValue([
      {
        validation: 'phoneNumbers',
        score: 100,
      },
    ]);
    jest.spyOn(validateNamesService, 'validate').mockResolvedValue([
      {
        validation: 'names',
        score: 100,
      },
    ]);

    const result = await service.validate(mockValidName);
    expect(validateRegistrationService.validate).toHaveBeenCalledWith({
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '1234567890',
      contacts: [
        {
          fullName: 'John Doe',
          phoneNumber: '1234567890',
        },
      ],
    });

    expect(result).toEqual({
      validator: 'contactsValidator',
      isValid: true,
      score: 100,
      context: expect.arrayContaining([
        {
          validation: 'registration',
          score: 100,
        },
        {
          validation: 'phoneNumbers',
          score: 100,
        },
        {
          validation: 'names',
          score: 100,
        },
      ]),
    });
  });

  it('should validate correct contacts', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '1234567890',
      contacts: [],
    };
    jest.spyOn(validateRegistrationService, 'validate').mockResolvedValue([
      {
        validation: 'registration',
        score: 100,
      },
    ]);
    jest.spyOn(validatePhoneNumbersService, 'validate').mockResolvedValue([
      {
        validation: 'phoneNumbers',
        score: 100,
      },
    ]);
    jest.spyOn(validateNamesService, 'validate').mockResolvedValue([
      {
        validation: 'names',
        score: 100,
      },
    ]);

    const result = await service.validate(mockValidName);

    expect(result).toEqual({
      validator: 'contactsValidator',
      isValid: true,
      score: 100,
      context: expect.arrayContaining([
        {
          validation: 'registration',
          score: 100,
        },
        {
          validation: 'phoneNumbers',
          score: 100,
        },
        {
          validation: 'names',
          score: 100,
        },
      ]),
    });
  });
});
