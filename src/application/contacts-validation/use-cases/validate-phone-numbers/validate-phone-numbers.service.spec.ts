import { Test, TestingModule } from '@nestjs/testing';
import { ValidatePhoneNumbersService } from './validate-phone-numbers.service';
import { PhoneNumberService } from 'src/infrastructure/phone-number/phone-number.service';
import { ValidateWhatsappService } from '../validate-whatsapp/validate-whatsapp.service';
import { faker } from '@faker-js/faker';
import { ZenpliService } from 'src/infrastructure/external-services/zenpli/zenpli.service';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';

describe('ValidatePhoneNumbersService', () => {
  let service: ValidatePhoneNumbersService;
  let phoneNumberService: PhoneNumberService;
  let zenpliService: ZenpliService;
  let validateWhatsappService: ValidateWhatsappService;
  let phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PhoneNumberService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ValidateWhatsappService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ZenpliService,
          useValue: {
            validateCustomer: jest.fn(),
            login: jest.fn().mockResolvedValue(faker.word.noun()),
          },
        },
        {
          provide: PhoneNumbersBlacklistRepository,
          useValue: {
            findByPhoneNumber: jest.fn(),
          },
        },
        ValidatePhoneNumbersService,
      ],
    }).compile();

    service = module.get<ValidatePhoneNumbersService>(ValidatePhoneNumbersService);
    phoneNumberService = module.get<PhoneNumberService>(PhoneNumberService);
    zenpliService = module.get<ZenpliService>(ZenpliService);
    validateWhatsappService = module.get<ValidateWhatsappService>(ValidateWhatsappService);
    phoneNumbersBlacklistRepository = module.get<PhoneNumbersBlacklistRepository>(
      PhoneNumbersBlacklistRepository,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  it('should validate a valid phoneNumber', async () => {
    const mockValidPhoneNumber = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '1234567890',
      contacts: [],
      userId: '1234567890',
      requestId: '1234567890',
      wholesaler: 'my-wholesaler',
    };
    jest.spyOn(phoneNumberService, 'validate').mockResolvedValue([
      {
        isValid: true,
        insights: {
          phone_line_type: 'MOBILE',
        },
        scores: {
          score: 100,
        },
        phoneNumber: '',
      },
    ]);

    jest.spyOn(zenpliService, 'validateCustomer').mockResolvedValue({
      isValid: true,
      insights: {
        phone_line_type: 'MOBILE',
      },
      scores: {
        score: 100,
      },
    });
    jest.spyOn(validateWhatsappService, 'validate').mockResolvedValue({
      validation: 'whatsappValidation',
      score: 0,
    });

    jest.spyOn(phoneNumbersBlacklistRepository, 'findByPhoneNumber').mockResolvedValue(null);

    jest.spyOn(service, 'validate').mockResolvedValue([
      {
        validation: 'phoneNumberValidation',
        score: 0,
      },
    ]);

    const result = await service.validate(mockValidPhoneNumber);

    expect(result).toEqual([
      {
        validation: 'phoneNumberValidation',
        score: 0,
      },
    ]);
  });
});
