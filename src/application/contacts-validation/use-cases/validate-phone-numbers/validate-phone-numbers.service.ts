import { Injectable } from '@nestjs/common';
import { ErrorCodes, ValidationNames } from 'src/application/constants';
import { UserFraudRiskContext } from 'src/application/score/interfaces/user-fraud-risk-response';
import { PhoneNumberService } from 'src/infrastructure/phone-number/phone-number.service';
import { ValidateWhatsappService } from '../validate-whatsapp/validate-whatsapp.service';
import { ContactsFraudRiskValidatorStrategy } from 'src/application/score/interfaces/user-fraud-risk-strategy.interface';
import { Scores } from 'src/application/score/constants';
import { ValidateUserPayload } from 'src/application/score/dtos/validate-user';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';
import { FraudRiskContext } from 'src/interfaces/fraud-risk-context.interface';

type PhoneNumberResult = {
  phoneNumber: string;
  isValid: boolean;
  insights: any;
  reason?: string;
  errorValue?: string;
  scores: any;
};

@Injectable()
export class ValidatePhoneNumbersService implements ContactsFraudRiskValidatorStrategy {
  constructor(
    private readonly phoneNumberService: PhoneNumberService,
    private readonly validateWhatsappService: ValidateWhatsappService,
    private readonly phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository,
  ) {}

  private getPhoneNumbersResults(
    phoneNumberResults: PhoneNumberResult[],
    insight: string,
    rule: boolean,
  ): UserFraudRiskContext {
    if (!phoneNumberResults.length || !rule) {
      return null;
    }

    return {
      validation: ValidationNames.PHONE_NUMBER_VALIDATION,
      score: phoneNumberResults.length > 1 ? Scores.MAX_VALUE : Scores.REVIEW_VALUE,
      errorCode: `INVALID_PHONE_NUMBER_${insight.toUpperCase()}`,
      errorMessage: `Invalid phone number: ${insight}`,
      errorValue: phoneNumberResults.map((result) => result.errorValue).join(', '),
    };
  }

  private async validateMobileNumberExistsInWhatsapp(
    phoneNumberResults: PhoneNumberResult[],
    rule: boolean,
  ): Promise<UserFraudRiskContext> {
    if (!rule) {
      return null;
    }

    const responses = [];
    for (const element of phoneNumberResults) {
      const response = await this.validateWhatsappService.validate(element.phoneNumber);

      if (response.score !== Scores.OK_VALUE) {
        responses.push(response);
      }
    }

    const results = responses.filter((response) => response);

    if (!results.length) {
      return null;
    }
    return {
      validation: ValidationNames.WHATSAPP_VALIDATION,
      score: results.length > 1 ? Scores.MAX_VALUE : Scores.REVIEW_VALUE,
      errorCode: ErrorCodes.CONTACT_INVALID_WHATSAPP.code,
      errorMessage: ErrorCodes.CONTACT_INVALID_WHATSAPP.message,
      errorValue: results.map((result) => result.errorValue).join(', '),
    };
  }

  private async validateBlacklistedNumbers(phoneNumberResults: PhoneNumberResult[]) {
    const responses = [];
    for (const element of phoneNumberResults) {
      const isBlacklisted = await this.phoneNumbersBlacklistRepository.findByPhoneNumber(
        element.phoneNumber,
      );

      if (isBlacklisted) {
        responses.push(element);
      }
    }

    if (!responses.length) {
      return null;
    }

    return {
      validation: ValidationNames.PHONE_NUMBER_VALIDATION,
      score: Scores.MAX_VALUE,
      errorCode: ErrorCodes.CONTACT_BLACKLISTED_NUMBER.code,
      errorMessage: ErrorCodes.CONTACT_BLACKLISTED_NUMBER.message,
      errorValue: responses.map((result) => result.phoneNumber).join(', '),
    };
  }

  private async handlePhoneNumberValidation(
    phoneNumbersResults: PhoneNumberResult[],
    phoneNumberValidation: FraudRiskContext['contactsValidatorRules']['phoneNumberValidation'],
  ): Promise<UserFraudRiskContext[]> {
    const mobilePhoneNumbers = phoneNumbersResults.filter(
      (result) => (result.insights.phone_line_type as string).toUpperCase() === 'MOBILE',
    );

    const invalidWhatsappNumbers = await this.validateMobileNumberExistsInWhatsapp(
      mobilePhoneNumbers,
      phoneNumberValidation.whatsappIsNotValid,
    );
    const invalidPhoneNumbers = phoneNumbersResults.filter((result) => !result.isValid);

    const phoneActiveValidation = this.getPhoneNumbersResults(
      invalidPhoneNumbers.filter(
        (result) => !result.insights.phone_active && result.insights.phone_active !== null,
      ),
      'phone_active',
      phoneNumberValidation.phoneNumberIsNotActive,
    );

    const validateCarrier = this.getPhoneNumbersResults(
      invalidPhoneNumbers.filter((result) => result.insights.phone_carrier === null),
      'phone_carrier',
      phoneNumberValidation.phoneNumberHasNoCarrier,
    );

    const phoneLineType = this.getPhoneNumbersResults(
      invalidPhoneNumbers.filter((result) => result.insights.phone_line_type === null),
      'phone_line_type',
      phoneNumberValidation.phoneLineTypeIsNotValid,
    );

    const validatePortedDays = this.getPhoneNumbersResults(
      invalidPhoneNumbers.filter(
        (result) =>
          result.insights.phone_ported_since_x_days !== null &&
          result.insights.phone_ported_since_x_days < 15,
      ),
      'phone_ported_since_x_days',
      phoneNumberValidation.phoneIsPortedSinceXDays,
    );

    const blacklistedNumbers = await this.validateBlacklistedNumbers(phoneNumbersResults);

    const results = [
      phoneActiveValidation,
      validateCarrier,
      phoneLineType,
      validatePortedDays,
      invalidWhatsappNumbers,
      blacklistedNumbers,
    ].filter((result) => result);

    if (!results.length) {
      return [
        {
          validation: ValidationNames.PHONE_NUMBER_VALIDATION,
          score: Scores.OK_VALUE,
        },
      ];
    }

    return results;
  }

  async validate(
    { contacts }: ValidateUserPayload,
    { contactsValidatorRules }: FraudRiskContext,
  ): Promise<UserFraudRiskContext[]> {
    const { phoneNumberValidation } = contactsValidatorRules;
    if (
      !phoneNumberValidation.phoneNumberIsNotValid &&
      !phoneNumberValidation.phoneNumberIsNotActive &&
      !phoneNumberValidation.phoneNumberHasNoCarrier &&
      !phoneNumberValidation.whatsappIsNotValid
    ) {
      return [];
    }

    const phoneNumbersResults = await this.phoneNumberService.validate(
      contacts.map((contact) => contact.phoneNumber),
    );

    const results = await this.handlePhoneNumberValidation(
      phoneNumbersResults,
      phoneNumberValidation,
    );
    return results;
  }
}
