import { Test, TestingModule } from '@nestjs/testing';
import { ValidateWhatsappService } from './validate-whatsapp.service';
import { WhatsappService } from 'src/infrastructure/whatsapp/whatsapp.service';

describe('ValidateWhatsappService', () => {
  let service: ValidateWhatsappService;
  let whatsappService: WhatsappService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: WhatsappService,
          useValue: {
            verifyPhoneNumber: jest.fn(),
          },
        },
        ValidateWhatsappService,
      ],
    }).compile();

    service = module.get<ValidateWhatsappService>(ValidateWhatsappService);
    whatsappService = module.get<WhatsappService>(WhatsappService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid whatsapp number', async () => {
    jest.spyOn(whatsappService, 'verifyPhoneNumber').mockResolvedValue({
      isValid: true,
      context: {},
      error: null,
    });

    const result = await service.validate('**********');

    expect(result).toEqual({
      validation: 'whatsappValidation',
      score: 0,
    });
  });

  it('should validate an invalid whatsapp number', async () => {
    jest.spyOn(whatsappService, 'verifyPhoneNumber').mockResolvedValue({
      isValid: false,
      context: {},
      error: {
        errorCode: 'WHATSAPP_NOT_EXISTS',
        errorMessage: 'This number doesnt have a whatsapp account',
        errorValue: '**********',
      },
    });

    const result = await service.validate('**********');

    expect(result).toEqual({
      validation: 'whatsappValidation',
      score: 50,
      errorCode: 'WHATSAPP_NOT_EXISTS',
      errorMessage: 'This number doesnt have a whatsapp account',
      errorValue: '**********',
    });
  });
});
