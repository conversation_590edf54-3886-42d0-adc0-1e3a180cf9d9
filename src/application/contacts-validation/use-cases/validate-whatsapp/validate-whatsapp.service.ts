import { Injectable } from '@nestjs/common';
import { ValidationNames } from 'src/application/constants';
import { Scores } from 'src/application/score/constants';
import { UserFraudRiskContext } from 'src/application/score/interfaces/user-fraud-risk-response';
import { WhatsappService } from 'src/infrastructure/whatsapp/whatsapp.service';

@Injectable()
export class ValidateWhatsappService {
  constructor(private readonly whatsappService: WhatsappService) {}

  async validate(phoneNumber: string): Promise<UserFraudRiskContext> {
    const validationResult = await this.whatsappService.verifyPhoneNumber(phoneNumber);

    if (validationResult.isValid) {
      return {
        validation: ValidationNames.WHATSAPP_VALIDATION,
        score: Scores.OK_VALUE,
      };
    }

    return {
      validation: ValidationNames.WHATSAPP_VALIDATION,
      score: Scores.REVIEW_VALUE,
      errorCode: validationResult.error.errorCode,
      errorMessage: validationResult.error.errorMessage,
      errorValue: validationResult.error.errorValue,
    };
  }
}
