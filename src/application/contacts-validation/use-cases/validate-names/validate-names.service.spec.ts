import { Test, TestingModule } from '@nestjs/testing';
import { ValidateNamesService } from './validate-names.service';
import { NameValidationService } from 'src/infrastructure/propaga-merlin/use-cases/name-validation/name-validation.service';

describe('ValidateNamesService', () => {
  let service: ValidateNamesService;
  let nameValidationService: NameValidationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: NameValidationService,
          useValue: {
            handler: jest.fn(),
          },
        },
        ValidateNamesService,
      ],
    }).compile();

    service = module.get<ValidateNamesService>(ValidateNamesService);
    nameValidationService = module.get<NameValidationService>(NameValidationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [
        {
          fullName: '<PERSON>e',
          phoneNumber: '**********',
        },
      ],
    };
    jest
      .spyOn(nameValidationService, 'handler')
      .mockResolvedValue({ isValid: true, score: 100, context: '' });

    const result = await service.validate(mockValidName);

    expect(result).toEqual([
      {
        validation: 'contactNameValidation',
        score: 0,
      },
    ]);
  });

  it('should validate an invalid name', async () => {
    const mockValidName = {
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [
        {
          fullName: 'Heinz Doofenshmirtz',
          phoneNumber: '**********',
        },
      ],
    };
    jest
      .spyOn(nameValidationService, 'handler')
      .mockResolvedValue({ isValid: false, score: 50, context: 'Invalid name' });

    const result = await service.validate(mockValidName);

    expect(result).toEqual([
      {
        validation: 'contactNameValidation',
        score: 50,
        errorCode: 'CONTACT_INVALID_NAME',
        errorMessage: 'Invalid name',
        errorValue: 'Heinz Doofenshmirtz',
      },
    ]);
  });
});
