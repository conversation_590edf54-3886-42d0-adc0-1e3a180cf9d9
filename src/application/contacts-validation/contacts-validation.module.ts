import { Module } from '@nestjs/common';
import { ContactsValidationStrategyService } from './use-cases/contacts-validation-strategy/contacts-validation-strategy.service';
import { PropagaMerlinModule } from 'src/infrastructure/propaga-merlin/propaga-merlin.module';
import { PropagaDatabaseModule } from 'src/infrastructure/propaga-database/propaga-database.module';
import { HttpModule } from '@nestjs/axios';
import { MongoDatabaseModule } from 'src/infrastructure/mongo-database/mongo-database.module';
import { ValidateRegistrationService } from './use-cases/validate-registration/validate-registration.service';
import { PhoneNumberModule } from 'src/infrastructure/phone-number/phone-number.module';
import { ValidatePhoneNumbersService } from './use-cases/validate-phone-numbers/validate-phone-numbers.service';
import { WhatsappModule } from 'src/infrastructure/whatsapp/whatsapp.module';
import { ValidateWhatsappService } from './use-cases/validate-whatsapp/validate-whatsapp.service';
import { ValidateNamesService } from './use-cases/validate-names/validate-names.service';

@Module({
  providers: [
    ContactsValidationStrategyService,
    ValidateRegistrationService,
    ValidatePhoneNumbersService,
    ValidateWhatsappService,
    ValidateNamesService,
  ],
  imports: [
    PropagaMerlinModule,
    PropagaDatabaseModule,
    HttpModule,
    MongoDatabaseModule,
    PhoneNumberModule,
    WhatsappModule,
  ],
  exports: [ContactsValidationStrategyService],
})
export class ContactsValidationModule {}
