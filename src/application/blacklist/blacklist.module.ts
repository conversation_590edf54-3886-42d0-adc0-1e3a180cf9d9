import { <PERSON>du<PERSON> } from '@nestjs/common';
import { BlacklistController } from './blacklist.controller';
import { AddPhoneNumberToBlacklistService } from './use-cases/add-phone-number-to-blacklist/add-phone-number-to-blacklist.service';
import { MongoDatabaseModule } from 'src/infrastructure/mongo-database/mongo-database.module';
import { GetPhoneNumberIsBlacklistedService } from './use-cases/get-phone-number-is-blacklisted/get-phone-number-is-blacklisted.service';

@Module({
  controllers: [BlacklistController],
  providers: [AddPhoneNumberToBlacklistService, GetPhoneNumberIsBlacklistedService],
  imports: [MongoDatabaseModule],
})
export class BlacklistModule {}
