import { Test, TestingModule } from '@nestjs/testing';
import { GetPhoneNumberIsBlacklistedService } from './get-phone-number-is-blacklisted.service';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';
import { faker } from '@faker-js/faker/.';
import { Types } from 'mongoose';

describe('GetPhoneNumberIsBlacklistedService', () => {
  let service: GetPhoneNumberIsBlacklistedService;
  let phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PhoneNumbersBlacklistRepository,
          useValue: {
            findByPhoneNumber: jest.fn(),
          },
        },
        GetPhoneNumberIsBlacklistedService,
      ],
    }).compile();

    service = module.get<GetPhoneNumberIsBlacklistedService>(GetPhoneNumberIsBlacklistedService);
    phoneNumbersBlacklistRepository = module.get<PhoneNumbersBlacklistRepository>(
      PhoneNumbersBlacklistRepository,
    );
  });

  it('should return a blacklisted phone number', () => {
    const phoneNumber = faker.phone.number();
    const reason = faker.lorem.text();

    expect(service).toBeDefined();
    jest.spyOn(phoneNumbersBlacklistRepository, 'findByPhoneNumber').mockResolvedValue({
      _id: new Types.ObjectId(),
      phoneNumber,
      reason,
      save: jest.fn(),
      toObject: jest.fn().mockReturnValue({
        _id: new Types.ObjectId(),
        phoneNumber,
        reason,
      }),
      $isDeleted: jest.fn(),
      $isModified: jest.fn(),
      $isNew: jest.fn(),
      $clone: jest.fn(),
    } as any);

    service.handler(phoneNumber);

    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toBeCalledTimes(1);
    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toBeCalledWith(phoneNumber);
    expect(service.handler(phoneNumber)).resolves.toEqual({
      isBlacklisted: true,
      phoneNumber,
      reason,
    });
  });

  it('should return a not blacklisted phone number', () => {
    const phoneNumber = faker.phone.number();

    jest.spyOn(phoneNumbersBlacklistRepository, 'findByPhoneNumber').mockResolvedValue(null);
    service.handler(phoneNumber);
    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toBeCalledTimes(1);
    expect(phoneNumbersBlacklistRepository.findByPhoneNumber).toBeCalledWith(phoneNumber);
    expect(service.handler(phoneNumber)).resolves.toEqual({
      isBlacklisted: false,
    });
  });
});
