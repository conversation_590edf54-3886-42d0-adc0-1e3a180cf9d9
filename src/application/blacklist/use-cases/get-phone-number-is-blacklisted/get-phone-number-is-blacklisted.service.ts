import { Injectable } from '@nestjs/common';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';

@Injectable()
export class GetPhoneNumberIsBlacklistedService {
  constructor(private readonly phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository) {}

  async handler(phoneNumber: string) {
    const result = await this.phoneNumbersBlacklistRepository.findByPhoneNumber(phoneNumber);

    if (!result) {
      return {
        isBlacklisted: false,
      };
    }

    return {
      isBlacklisted: true,
      phoneNumber: result.phoneNumber,
      reason: result.reason,
    };
  }
}
