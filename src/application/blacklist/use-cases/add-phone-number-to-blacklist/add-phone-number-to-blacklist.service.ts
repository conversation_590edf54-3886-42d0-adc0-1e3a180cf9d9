import { Injectable } from '@nestjs/common';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';

@Injectable()
export class AddPhoneNumberToBlacklistService {
  constructor(private readonly phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository) {}

  async handler(phoneNumber: string, reason: string) {
    return this.phoneNumbersBlacklistRepository.create({ phoneNumber, reason });
  }
}
