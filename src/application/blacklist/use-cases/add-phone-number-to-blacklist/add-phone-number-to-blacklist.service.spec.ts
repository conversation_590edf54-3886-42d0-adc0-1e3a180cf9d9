import { Test, TestingModule } from '@nestjs/testing';
import { AddPhoneNumberToBlacklistService } from './add-phone-number-to-blacklist.service';
import { faker } from '@faker-js/faker/.';
import { PhoneNumbersBlacklistRepository } from 'src/infrastructure/mongo-database/repositories/phone-numbers-blacklist.service';

describe('AddPhoneNumberToBlacklistService', () => {
  let service: AddPhoneNumberToBlacklistService;
  let phoneNumbersBlacklistRepository: PhoneNumbersBlacklistRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PhoneNumbersBlacklistRepository,
          useValue: {
            create: jest.fn(),
          },
        },
        AddPhoneNumberToBlacklistService,
      ],
    }).compile();

    service = module.get<AddPhoneNumberToBlacklistService>(AddPhoneNumberToBlacklistService);
    phoneNumbersBlacklistRepository = module.get<PhoneNumbersBlacklistRepository>(
      PhoneNumbersBlacklistRepository,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should add a phone number to blacklist', async () => {
    const phoneNumber = faker.phone.number();
    const reason = faker.lorem.text();

    jest.spyOn(phoneNumbersBlacklistRepository, 'create').mockResolvedValue({
      phoneNumber,
      reason,
    });

    const result = await service.handler(phoneNumber, reason);

    expect(result).toBeDefined();
    expect(phoneNumbersBlacklistRepository.create).toHaveBeenCalledWith({ phoneNumber, reason });
    expect(result).toHaveProperty('phoneNumber');
    expect(result).toHaveProperty('reason');
  });
});
