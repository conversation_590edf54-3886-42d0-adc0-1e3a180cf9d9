import { Body, Controller, Get, Param, Post, UseInterceptors } from '@nestjs/common';
import { SentryInterceptor } from 'src/middleware/error.interceptor';
import { AddPhoneNumberToBlacklistService } from './use-cases/add-phone-number-to-blacklist/add-phone-number-to-blacklist.service';
import { AddPhoneNumberToBlacklistDTO } from './dtos/add-phone-number-to-blacklist';
import { GetPhoneNumberIsBlacklistedService } from './use-cases/get-phone-number-is-blacklisted/get-phone-number-is-blacklisted.service';

@UseInterceptors(SentryInterceptor)
@Controller('blacklist')
export class BlacklistController {
  constructor(
    private readonly addPhoneNumberToBlacklistService: AddPhoneNumberToBlacklistService,
    private readonly getPhoneNumberIsBlacklistedService: GetPhoneNumberIsBlacklistedService,
  ) {}

  @Post('/phone-number')
  async addPhoneNumberToBlacklist(@Body() { phoneNumber, reason }: AddPhoneNumberToBlacklistDTO) {
    return this.addPhoneNumberToBlacklistService.handler(phoneNumber, reason);
  }

  @Get('/phone-number/:phoneNumber')
  async getPhoneNumberIsBlacklisted(@Param('phoneNumber') phoneNumber: string) {
    return this.getPhoneNumberIsBlacklistedService.handler(phoneNumber);
  }
}
