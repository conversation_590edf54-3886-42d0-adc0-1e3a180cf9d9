export const SUCCESSFUL_RESPONSE = Object.freeze({
  fraudRiskScore: 0,
  validations: [
    {
      validator: 'userValidator',
      isValid: true,
      score: 0,
      context: [
        { validation: 'geolocationValidation', score: 0 },
        { validation: 'nameValidation', score: 0 },
        { validation: 'metadataValidation', score: 0 },
      ],
    },
    {
      validator: 'contactsValidator',
      isValid: true,
      score: 0,
      context: [
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'registrationValidation', score: 0 },
        { validation: 'phoneNumberValidation', score: 0 },
      ],
    },
  ],
});

export const FAILED_RESPONSE = Object.freeze({
  fraudRiskScore: 100,
  validations: [
    {
      validator: 'userValidator',
      isValid: false,
      score: 100,
      context: [
        { validation: 'geolocationValidation', score: 0 },
        { validation: 'nameValidation', score: 0 },
        { validation: 'metadataValidation', score: 0 },
      ],
    },
    {
      validator: 'contactsValidator',
      isValid: false,
      score: 100,
      context: [
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'registrationValidation', score: 0 },
        {
          validation: 'phoneNumberValidation',
          score: 100,
          errorCode: 'INVALID_PHONE_NUMBER_PHONE_ACTIVE',
          errorMessage: 'Invalid phone number: phone_active',
          errorValue: '1234567890, 0987654321',
        },
      ],
    },
  ],
});

export const REVIEW_RESPONSE = Object.freeze({
  fraudRiskScore: 50,
  validations: [
    {
      validator: 'userValidator',
      isValid: false,
      score: 0,
      context: [
        { validation: 'geolocationValidation', score: 0 },
        { validation: 'nameValidation', score: 0 },
        { validation: 'metadataValidation', score: 0 },
      ],
    },
    {
      validator: 'contactsValidator',
      isValid: false,
      score: 50,
      context: [
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'registrationValidation', score: 0 },
        {
          validation: 'phoneNumberValidation',
          score: 50,
          errorCode: 'INVALID_PHONE_NUMBER_PHONE_ACTIVE',
          errorMessage: 'Invalid phone number: phone_active',
          errorValue: '1234567890',
        },
      ],
    },
  ],
});

export const CONTACT_IS_AUTORREFERED = Object.freeze({
  fraudRiskScore: 100,
  validations: [
    {
      validator: 'userValidator',
      isValid: false,
      score: 0,
      context: [
        { validation: 'geolocationValidation', score: 0 },
        { validation: 'nameValidation', score: 0 },
        { validation: 'metadataValidation', score: 0 },
      ],
    },
    {
      validator: 'contactsValidator',
      isValid: false,
      score: 100,
      context: [
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'contactNameValidation', score: 0 },
        {
          validation: 'registrationValidation',
          score: 100,
          errorCode: 'CONTACT_IS_AUTORREFERED',
          errorMessage: 'This number is autorreferred',
          errorValue: 'Test user (521234567890)',
        },
        { validation: 'phoneNumberValidation', score: 0 },
      ],
    },
  ],
});

export const CONTACT_INVALID_WHATSAPP = Object.freeze({
  fraudRiskScore: 100,
  validations: [
    {
      validator: 'userValidator',
      isValid: false,
      score: 0,
      context: [
        { validation: 'geolocationValidation', score: 0 },
        { validation: 'nameValidation', score: 0 },
        { validation: 'metadataValidation', score: 0 },
      ],
    },
    {
      validator: 'contactsValidator',
      isValid: false,
      score: 100,
      context: [
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'contactNameValidation', score: 0 },
        { validation: 'registrationValidation', score: 0 },
        {
          validation: 'whatsappValidation',
          score: 100,
          errorCode: 'CONTACT_INVALID_WHATSAPP',
          errorMessage: 'This number doesnt have a whatsapp account',
          errorValue: ', ',
        },
      ],
    },
  ],
});
