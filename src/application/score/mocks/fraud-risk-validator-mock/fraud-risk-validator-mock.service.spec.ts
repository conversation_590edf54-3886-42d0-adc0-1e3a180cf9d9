import { Test, TestingModule } from '@nestjs/testing';
import { FraudRiskValidatorMockService } from './fraud-risk-validator-mock.service';
import { FAILED_RESPONSE, SUCCESSFUL_RESPONSE } from '../responses/fraud-risk-validator.responses';

describe('FraudRiskValidatorMockService', () => {
  let service: FraudRiskValidatorMockService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FraudRiskValidatorMockService],
    }).compile();

    service = module.get<FraudRiskValidatorMockService>(FraudRiskValidatorMockService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return a successful response', async () => {
    const result = await service.handler({
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [],
      metadata: {
        mockValidationResponse: 'SUCCESS',
      },
    });

    expect(result).toEqual(SUCCESSFUL_RESPONSE);
  });

  it('should return a failed response', async () => {
    const result = await service.handler({
      firstName: 'John',
      lastNames: 'Doe',
      phoneNumber: '**********',
      contacts: [],
      metadata: {
        mockValidationResponse: 'FAILED',
      },
    });

    expect(result).toEqual(FAILED_RESPONSE);
  });
});
