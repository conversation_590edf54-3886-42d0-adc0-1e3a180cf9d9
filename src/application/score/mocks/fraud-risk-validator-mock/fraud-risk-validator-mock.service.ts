import { Injectable } from '@nestjs/common';
import { UserPayloadDTO } from '../../dtos/validate-user';
import {
  CONTACT_INVALID_WHATSAPP,
  CONTACT_IS_AUTORREFERED,
  FAILED_RESPONSE,
  REVIEW_RESPONSE,
  SUCCESSFUL_RESPONSE,
} from '../responses/fraud-risk-validator.responses';
import { MOCKED_RESPONSE_TYPES } from 'src/infrastructure/constants';

@Injectable()
export class FraudRiskValidatorMockService {
  async handler(userPayload: UserPayloadDTO) {
    const { metadata } = userPayload;
    switch (metadata['mockValidationResponse']) {
      case MOCKED_RESPONSE_TYPES.SUCCESS:
        return SUCCESSFUL_RESPONSE;
      case MOCKED_RESPONSE_TYPES.FAILED:
        return FAILED_RESPONSE;
      case MOCKED_RESPONSE_TYPES.REVIEW:
        return REVIEW_RESPONSE;
      case MOCKED_RESPONSE_TYPES.CONTACT_IS_AUTORREFERED:
        return CONTACT_IS_AUTORREFERED;
      case MOCKED_RESPONSE_TYPES.CONTACT_INVALID_WHATSAPP:
        return CONTACT_INVALID_WHATSAPP;
      default:
        return SUCCESSFUL_RESPONSE;
    }
  }
}
