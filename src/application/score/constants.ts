const Scores = Object.freeze({
  MAX_VALUE: 100,
  REVIEW_VALUE: 50,
  OK_VALUE: 0,
});

const Reasons = Object.freeze({
  SOME_CONTACT_IS_IN_DEFAULT: 'evaluateSomeContactIsInDefault',
  EVERY_CONTACT_IS_INACTIVE: 'evaluateEveryContactIsInactive',
  SOME_CONTACT_IS_INACTIVE: 'evaluateSomeContactIsInactive',
  GEOLOCATION: 'evaluateGeolocation',
  CONTACT_CHANNELS: 'evaluateContactChannels',
  AUTORREFERED: 'evaluateAutorrefered',
  LAND_LINE: 'evaluateLandLine',
});

const PhoneLineTypes = Object.freeze({
  NON_FIXED_VOIP: 'non-fixed-voip',
  PREMIUM: 'premium',
  VOICEMAIL: 'voicemail',
  LANDLINE: 'landline',
  FIXED_VOIP: 'fixed-voip',
  TOLL_FREE: 'toll-free',
  OTHER: 'other',
  MO<PERSON>LE: 'mobile',
});

export { Scores, Reasons, PhoneLineTypes };
