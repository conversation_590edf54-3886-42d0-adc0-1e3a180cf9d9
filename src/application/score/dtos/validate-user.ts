import {
  Is<PERSON><PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ongitude,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsPhoneNumberValid } from './phone-validator';

export class Contact {
  @IsNotEmpty()
  @IsString()
  fullName: string;

  @IsNotEmpty()
  @IsPhoneNumberValid()
  phoneNumber: string;
}

export class UserPayloadDTO {
  @IsString()
  @IsNotEmpty()
  @IsPhoneNumberValid()
  public phoneNumber: string;

  @IsNotEmpty()
  @IsLatitude()
  public latitude?: string;

  @IsNotEmpty()
  @IsLongitude()
  public longitude?: string;

  @IsNotEmpty()
  public firstName: string;

  @IsNotEmpty()
  public lastNames: string;

  @IsNotEmpty()
  @IsArray()
  @Type(() => Contact)
  @ValidateNested({ each: true })
  public contacts: Contact[];

  @IsOptional()
  @IsObject()
  public metadata?: object;

  @IsNotEmpty()
  public wholesaler: string;
}

export class ValidateUserPayload {
  phoneNumber: string;
  latitude?: string;
  longitude?: string;
  firstName: string;
  lastNames: string;
  contacts: Contact[];
  metadata?: object;
}
