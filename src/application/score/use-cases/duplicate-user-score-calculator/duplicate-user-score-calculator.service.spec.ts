import { Test, TestingModule } from '@nestjs/testing';
import { DuplicateUserScoreCalculatorService } from './duplicate-user-score-calculator.service';
import { UserRepository } from 'src/infrastructure/propaga-database/repositories/user.repository';
import { Users } from 'src/infrastructure/propaga-database/models/users.entity';

describe('DuplicateUserScoreCalculatorService', () => {
  let service: DuplicateUserScoreCalculatorService;
  let userRepository: UserRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: UserRepository,
          useValue: {
            findUserByPhoneNumber: jest.fn(),
          },
        },
        DuplicateUserScoreCalculatorService,
      ],
    }).compile();

    service = module.get<DuplicateUserScoreCalculatorService>(DuplicateUserScoreCalculatorService);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    const mockValidName = {
      userId: '**********',
      phoneNumber: '**********',
    };
    jest.spyOn(userRepository, 'findUserByPhoneNumber').mockResolvedValue(null as Users);

    const result = await service.handler(mockValidName);

    expect(result).toEqual({
      score: 0,
    });
  });

  it('should expect a score of a duplicated user', async () => {
    const mockDupUser = {
      userId: '541fd347-d83d-4749-a17b-bf25495736a5',
      phoneNumber: '**********',
    };

    const samePhoneUserId = 'aa1d920a-fa37-4917-b0e6-2f66e8786860';
    jest.spyOn(userRepository, 'findUserByPhoneNumber').mockResolvedValue({
      firstName: 'Joel',
      lastNames: 'Doe',
      phoneNumber: '**********',
      id: samePhoneUserId,
    } as Users);

    const result = await service.handler(mockDupUser);

    expect(result).toEqual({
      score: 100,
      context: {
        userId: samePhoneUserId,
      },
    });
  });
});
