import { Injectable } from '@nestjs/common';
import { DuplicateUserScores } from 'src/application/constants';
import { UserRepository } from 'src/infrastructure/propaga-database/repositories/user.repository';

@Injectable()
export class DuplicateUserScoreCalculatorService {
  constructor(private readonly userRepository: UserRepository) {}

  async handler({ userId, phoneNumber }: { userId: string; phoneNumber: string }): Promise<object> {
    const userWithSamePhoneNumber = await this.userRepository.findUserByPhoneNumber(
      userId,
      phoneNumber,
    );

    if (userWithSamePhoneNumber) {
      return {
        score: DuplicateUserScores.MAX_VALUE,
        context: {
          userId: userWithSamePhoneNumber.id,
        },
      };
    }

    return {
      score: DuplicateUserScores.OK_VALUE,
    };
  }
}
