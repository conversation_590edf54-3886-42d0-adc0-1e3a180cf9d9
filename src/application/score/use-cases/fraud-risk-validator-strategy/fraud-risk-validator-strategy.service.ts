import { Injectable } from '@nestjs/common';
import { UserValidationStrategyService } from '../../../user-validation/use-cases/user-validation-strategy/user-validation-strategy.service';
import { ContactsValidationStrategyService } from 'src/application/contacts-validation/use-cases/contacts-validation-strategy/contacts-validation-strategy.service';
import { UserFraudRiskResult } from '../../interfaces/user-fraud-risk-response';
import { UserPayloadDTO } from '../../dtos/validate-user';
import { Environments } from 'src/infrastructure/constants';
import { FraudRiskValidatorMockService } from '../../mocks/fraud-risk-validator-mock/fraud-risk-validator-mock.service';
import { Scores } from '../../constants';
import { WholesalerRepository } from 'src/infrastructure/propaga-database/repositories/wholesaler.repository';
import { FraudRiskContext } from '../../../../interfaces/fraud-risk-context.interface';

@Injectable()
export class FraudRiskValidatorStrategyService {
  constructor(
    private readonly userValidationStrategyService: UserValidationStrategyService,
    private readonly contactsValidationStrategyService: ContactsValidationStrategyService,
    private readonly fraudRiskValidatorMockService: FraudRiskValidatorMockService,
    private readonly wholesalerRepository: WholesalerRepository,
  ) {}

  private isMockRequest({ metadata }: UserPayloadDTO) {
    if (process.env.NODE_ENV === Environments.PRODUCTION) {
      return false;
    }

    if (!metadata) {
      return false;
    }

    return !!metadata['mockValidationResponse'];
  }

  async handler(userPayload: UserPayloadDTO) {
    if (this.isMockRequest(userPayload)) {
      return this.fraudRiskValidatorMockService.handler(userPayload);
    }

    const wholesaler = await this.wholesalerRepository.findWholesalerByName(userPayload.wholesaler);

    if (!wholesaler) {
      throw new Error('Wholesaler not found');
    }

    const context = wholesaler.userValidationRules as FraudRiskContext;

    const validators = [
      this.userValidationStrategyService.validate(userPayload, context),
      this.contactsValidationStrategyService.validate(userPayload, context),
    ];

    const responses = await Promise.allSettled(validators);
    const results = responses.map((response) => response['value']) as UserFraudRiskResult[];

    return {
      fraudRiskScore: results.reduce((max, current) => {
        if (current && current.score) {
          return Scores.REVIEW_VALUE;
        }
        return Math.max(max, 0);
      }, Number.MIN_SAFE_INTEGER),
      validations: results,
    };
  }
}
