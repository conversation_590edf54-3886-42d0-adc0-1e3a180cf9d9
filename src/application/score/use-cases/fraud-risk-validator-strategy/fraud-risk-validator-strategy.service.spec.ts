import { Test, TestingModule } from '@nestjs/testing';
import { FraudRiskValidatorStrategyService } from './fraud-risk-validator-strategy.service';
import { UserValidationStrategyService } from 'src/application/user-validation/use-cases/user-validation-strategy/user-validation-strategy.service';
import { ContactsValidationStrategyService } from 'src/application/contacts-validation/use-cases/contacts-validation-strategy/contacts-validation-strategy.service';
import { FraudRiskValidatorMockService } from '../../mocks/fraud-risk-validator-mock/fraud-risk-validator-mock.service';
import { SUCCESSFUL_RESPONSE } from '../../mocks/responses/fraud-risk-validator.responses';

describe('FraudRiskValidatorStrategyService', () => {
  let service: FraudRiskValidatorStrategyService;
  let userValidationStrategyService: UserValidationStrategyService;
  let contactsValidationStrategyService: ContactsValidationStrategyService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: UserValidationStrategyService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: ContactsValidationStrategyService,
          useValue: {
            validate: jest.fn(),
          },
        },
        {
          provide: FraudRiskValidatorMockService,
          useValue: {
            handler: jest.fn().mockReturnValue(SUCCESSFUL_RESPONSE),
          },
        },
        FraudRiskValidatorStrategyService,
      ],
    }).compile();

    service = module.get<FraudRiskValidatorStrategyService>(FraudRiskValidatorStrategyService);
    userValidationStrategyService = module.get<UserValidationStrategyService>(
      UserValidationStrategyService,
    );
    contactsValidationStrategyService = module.get<ContactsValidationStrategyService>(
      ContactsValidationStrategyService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should validate a valid name', async () => {
    jest.spyOn(userValidationStrategyService, 'validate').mockResolvedValue({
      validator: 'userValidator',
      isValid: true,
      score: 100,
      context: [],
    });
    jest.spyOn(contactsValidationStrategyService, 'validate').mockResolvedValue({
      validator: 'contactsValidator',
      isValid: true,
      score: 100,
      context: [],
    });
    const result = await service.handler({
      phoneNumber: '1234567890',
      firstName: 'John',
      lastNames: 'Doe',
      contacts: [],
    });

    expect(result).toEqual({
      fraudRiskScore: 50,
      validations: expect.arrayContaining([
        {
          validator: 'userValidator',
          isValid: true,
          score: 100,
          context: [],
        },
        {
          validator: 'contactsValidator',
          isValid: true,
          score: 100,
          context: [],
        },
      ]),
    });
  });

  it('should return a mocked response when metadata flag is present', async () => {
    const result = await service.handler({
      phoneNumber: '1234567890',
      firstName: 'John',
      lastNames: 'Doe',
      contacts: [],
      metadata: {
        mockValidationResponse: 'SUCCESS',
      },
    });

    expect(result).toEqual(SUCCESSFUL_RESPONSE);
  });
});
