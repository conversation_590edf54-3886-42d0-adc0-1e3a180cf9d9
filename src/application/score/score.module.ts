import { Modu<PERSON> } from '@nestjs/common';
import { ScoreController } from './score.controller';
import { DuplicateUserScoreCalculatorService } from './use-cases/duplicate-user-score-calculator/duplicate-user-score-calculator.service';
import { PropagaDatabaseModule } from 'src/infrastructure/propaga-database/propaga-database.module';
import { UserValidationModule } from '../user-validation/user-validation.module';
import { ContactsValidationModule } from '../contacts-validation/contacts-validation.module';
import { FraudRiskValidatorStrategyService } from './use-cases/fraud-risk-validator-strategy/fraud-risk-validator-strategy.service';
import { FraudRiskValidatorMockService } from './mocks/fraud-risk-validator-mock/fraud-risk-validator-mock.service';

@Module({
  controllers: [ScoreController],
  providers: [
    DuplicateUserScoreCalculatorService,
    FraudRiskValidatorStrategyService,
    FraudRiskValidatorMockService,
  ],
  imports: [PropagaDatabaseModule, UserValidationModule, ContactsValidationModule],
})
export class ScoreModule {}
