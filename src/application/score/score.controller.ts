import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { DuplicateUserScoreCalculatorService } from './use-cases/duplicate-user-score-calculator/duplicate-user-score-calculator.service';
import { DuplicateUserScoreCalculatorDTO } from './dtos/duplicate-user-score-calculator';
import { FraudRiskValidatorStrategyService } from './use-cases/fraud-risk-validator-strategy/fraud-risk-validator-strategy.service';
import { UserPayloadDTO } from './dtos/validate-user';
import { SentryInterceptor } from 'src/middleware/error.interceptor';
@UseInterceptors(SentryInterceptor)
@Controller('score')
export class ScoreController {
  constructor(
    private readonly duplicateUserScoreCalculatorService: DuplicateUserScoreCalculatorService,
    private readonly fraudRiskValidatorStrategyService: FraudRiskValidatorStrategyService,
  ) {}

  @Post('/duplicate-user')
  async getDuplicateUserScore(@Body() userPayload: DuplicateUserScoreCalculatorDTO) {
    return this.duplicateUserScoreCalculatorService.handler(userPayload);
  }

  @Post('/fraud-risk')
  async getFraudRiskScore(@Body() userPayload: UserPayloadDTO) {
    return this.fraudRiskValidatorStrategyService.handler(userPayload);
  }
}
